// Content script for Perplexity Web MCP Bridge
// This script runs in the page context and communicates with the bridge

(function () {
  'use strict';

  // Prevent multiple initializations
  if (window.mcpContentScriptLoaded) {
    console.log('[Perplexity MCP] Content script already loaded, skipping');
    return;
  }
  window.mcpContentScriptLoaded = true;

  // Check if already initialized
  if (window.mcpClient && window.mcpClient.isInitialized) {
    console.log('[Perplexity MCP] Client already initialized, skipping');
    return;
  }

  class PerplexityMcpClient {
    constructor() {
      // Removed WebSocket related properties: this.ws, this.isConnecting, this.reconnectAttempts, this.maxReconnectAttempts
      this.isConnected = false; // Will be updated by background script messages
      this.mcpServers = []; // Will be updated by background script messages
      this.pendingRequests = new Map(); // For tracking requests sent via background
      this.requestId = 0;
      this.isInitialized = false;
      // this.statusCheckInterval = null; // Connection checks handled by background

      this.settings = {}; // Initialize as empty, will be loaded

      // Response monitoring
      this.responseObserver = null;
      this.lastProcessedResponseCount = 0;

      // Seamless mode state tracking
      this.seamlessMode = {
        activeToolCalls: new Map(), // Track active tool calls and their states
        hiddenTextarea: null, // Hidden textarea for background MCP operations
        userTextarea: null, // User-facing textarea
        responseElementCount: 0, // Track response element count (potentially for deletions)
        pendingDeletions: [], // Queue of elements to delete after tool responses
        threadState: new Map(), // State persistence per thread/URL
        lastPbLgCount: 0, // For tracking new .pb-lg elements in seamless mode
        MAX_PROCESSING_ATTEMPTS: 3,
        PROCESSING_RETRY_DELAYS: [500, 1000, 1500] // ms
      };

      // Enhanced state persistence system
      this.stateManager = {
        isRestoring: false, // Flag to prevent tool execution during restoration
        restorationComplete: false, // Flag to track restoration completion
        modifiedElements: new Map(), // Track all modified DOM elements
        elementStates: new Map(), // Store element states for restoration
        widgetStates: new Map(), // Store widget states
        queryCleanupStates: new Map(), // Store query cleanup states
        statusIndicatorStates: new Map(), // Store status indicator states
        lastSaveTime: 0, // Track last save time to prevent excessive saves
        saveThrottleMs: 1000 // Minimum time between saves
      };

      this.init();
      // Prevent duplicate rapid submissions of tool results
      this.isSubmittingToolResult = false;
    }

    // Background text sending method to avoid UI issues
    sendTextInBackground(textarea, text) {
      if (!textarea) {
        console.error('[Perplexity MCP] No textarea provided to sendTextInBackground');
        return false;
      }

      try {
        console.log('[Perplexity MCP] Setting text using background method:', text.substring(0, 100) + '...');

        // React-compatible native setter (recommended)
        const nativeSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value').set;
        nativeSetter.call(textarea, text);
        textarea.dispatchEvent(new Event('input', { bubbles: true }));

        console.log('[Perplexity MCP] Text set using native setter method');
        return true;
      } catch (error) {
        console.error('[Perplexity MCP] Error in sendTextInBackground:', error);
        return false;
      }
    }

    // Background submission method to avoid UI issues
    submitTextInBackground(textarea) {
      if (!textarea) {
        console.error('[Perplexity MCP] No textarea provided to submitTextInBackground');
        return false;
      }

      try {
        console.log('[Perplexity MCP] Submitting using background method');

        // Find the form or submission handler
        const form = textarea.closest('form');
        const submitButton = document.querySelector('button[type="submit"], button[aria-label*="Submit"]');

        if (submitButton) {
          // Temporarily remove our own event handlers to prevent circular calls
          const originalHandler = submitButton.mcpClickHandler;
          if (originalHandler) {
            submitButton.removeEventListener('click', originalHandler, { capture: true });
          }

          // Create and dispatch the click event
          const submitEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          submitEvent.mcpProcessed = true; // Mark to prevent interception

          // Dispatch directly to submit button
          submitButton.dispatchEvent(submitEvent);
          console.log('[Perplexity MCP] Direct submission bypassed UI completely');

          // Re-add our handler after a short delay
          if (originalHandler) {
            setTimeout(() => {
              submitButton.addEventListener('click', originalHandler, { capture: true });
            }, 100);
          }

          return true;
        } else {
          console.warn('[Perplexity MCP] No submit button found for background submission');
          return false;
        }
      } catch (error) {
        console.error('[Perplexity MCP] Error in submitTextInBackground:', error);
        return false;
      }
    }

    // Identical to background.js and settings.js
    getDefaultSettings() {
      return {
        bridgeEnabled: true,
        autoConnect: true,
        bridgeUrl: 'ws://localhost:54319',
        alwaysInject: false,
        reconnectAttempts: 5,
        connectionTimeout: 5000,
        autoExecute: true,
        executionTimeout: 30000,
        autoDiscoverServers: true,
        serverSettings: {}, // Stored as object, not Map, in chrome.storage
        showStatusPanel: true,
        panelPosition: 'bottom-left',
        showToolResults: true,
        resultStyle: 'inline',
        debugLogging: false,
        verboseLogging: false,
        legacyMode: false // New setting for legacy behavior
      };
    }

    async loadSettings() {
      return new Promise((resolve) => {
        chrome.storage.sync.get(['mcpSettings'], (result) => {
          const loadedSettings = result.mcpSettings || {};
          this.settings = { ...this.getDefaultSettings(), ...loadedSettings };
          // Convert serverSettings back to Map if needed, though direct object usage might be simpler
          // if (this.settings.serverSettings && !(this.settings.serverSettings instanceof Map)) {
          //    this.settings.serverSettings = new Map(Object.entries(this.settings.serverSettings));
          // } else if (!this.settings.serverSettings) {
          //    this.settings.serverSettings = new Map();
          // }
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Settings loaded:', this.settings);
          }
          this.applyCurrentSettings();
          resolve();
        });
      });
    }

    applyCurrentSettings() {
      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] Applying settings:', this.settings);
      }
      this.updateStatusIndicatorsVisibility();
      this.updateStatusPanelPosition();

      // Handle legacy mode changes
      if (this.settings.legacyMode) {
        // Switch to legacy mode
        this.disableSeamlessMode();
      } else {
        // Switch to seamless mode
        this.enableSeamlessMode();
      }
    }

    enableSeamlessMode() {
      if (this.settings.bridgeEnabled && !this.seamlessMode.responseObserver) {
        console.log('[Perplexity MCP] Enabling seamless mode...');
        this.initializeSeamlessMode();
      }
    }

    disableSeamlessMode() {
      if (this.seamlessMode.responseObserver) {
        console.log('[Perplexity MCP] Disabling seamless mode...');

        // Stop seamless monitoring
        this.seamlessMode.responseObserver.disconnect();
        this.seamlessMode.responseObserver = null;

        // Restore original textarea visibility
        if (this.seamlessMode.hiddenTextarea) {
          this.seamlessMode.hiddenTextarea.style.opacity = '';
          this.seamlessMode.hiddenTextarea.style.pointerEvents = '';
        }

        // Remove overlay textarea
        if (this.seamlessMode.userTextarea && this.seamlessMode.userTextarea.parentNode) {
          this.seamlessMode.userTextarea.parentNode.removeChild(this.seamlessMode.userTextarea);
        }

        // Clean up resize observer
        if (this.seamlessMode.resizeObserver) {
          this.seamlessMode.resizeObserver.disconnect();
          this.seamlessMode.resizeObserver = null;
        }

        // Clean up all real-time monitoring observers
        this.cleanupAllObservers();

        // Clear references
        this.seamlessMode.hiddenTextarea = null;
        this.seamlessMode.userTextarea = null;

        // Clear state
        this.seamlessMode.activeToolCalls.clear();
        this.seamlessMode.pendingDeletions = [];

        console.log('[Perplexity MCP] Seamless mode disabled');
      }
    }

    // Clean up all MutationObservers to prevent memory leaks
    cleanupAllObservers() {
      console.log('[Perplexity MCP] Cleaning up all real-time monitoring observers...');

      // Stop query cleanup observer
      this.stopRealtimeQueryCleanup();

      // Stop response monitoring
      this.stopResponseMonitoring();

      // Clean up any prompt input monitoring observers
      if (this.promptInputMonitor) {
        this.promptInputMonitor.disconnect();
        this.promptInputMonitor = null;
      }

      // Clean up textarea appearance monitoring observers
      if (this.textareaAppearanceMonitor) {
        this.textareaAppearanceMonitor.disconnect();
        this.textareaAppearanceMonitor = null;
      }

      // Clean up input observation
      if (this.inputObserver) {
        this.inputObserver.disconnect();
        this.inputObserver = null;
      }

      // Clean up button observer
      if (this.buttonObserver) {
        this.buttonObserver.disconnect();
        this.buttonObserver = null;
      }

      console.log('[Perplexity MCP] ✅ All observers cleaned up');
    }

    updateStatusIndicatorsVisibility() {
      const statusPanel = document.getElementById('mcp-tools-status');
      if (statusPanel) {
        if (this.settings.showStatusPanel) {
          statusPanel.style.display = 'flex'; // or 'block' depending on its default
          if (this.settings.debugLogging) console.log('[Perplexity MCP] Status panel shown.');
        } else {
          statusPanel.style.display = 'none';
          if (this.settings.debugLogging) console.log('[Perplexity MCP] Status panel hidden.');
        }
      }
    }

    updateStatusPanelPosition() {
      const statusPanel = document.getElementById('mcp-tools-status');
      if (statusPanel) {
        // Remove all possible position classes
        statusPanel.classList.remove(
          'mcp-status-top-left',
          'mcp-status-top-right',
          'mcp-status-bottom-left',
          'mcp-status-bottom-right',
          'mcp-status-left',
          'mcp-status-right'
        );

        let positionClass = '';
        switch (this.settings.panelPosition) {
          case 'top-left':
            positionClass = 'mcp-status-top-left';
            break;
          case 'top-right':
          default:
            positionClass = 'mcp-status-top-right';
            break;
          case 'bottom-left':
            positionClass = 'mcp-status-bottom-left';
            break;
          case 'bottom-right':
            positionClass = 'mcp-status-bottom-right';
            break;
        }
        if (positionClass) {
          statusPanel.classList.add(positionClass);
        }
        if (this.settings.debugLogging) {
          console.log(`[Perplexity MCP] Status panel position set to: ${this.settings.panelPosition} (class: ${positionClass})`);
        }

        // Force a re-render to ensure position changes are applied
        statusPanel.style.display = 'none';
        statusPanel.offsetHeight; // Force reflow
        statusPanel.style.display = 'flex';
      }
    }

    async init() {
      if (this.isInitialized) {
        console.log('[Perplexity MCP] Already initialized, skipping');
        return;
      }
      console.log('[Perplexity MCP] Initializing content script...');
      this.isInitialized = true;

      await this.loadSettings();
      this.setupGlobalMcpInterface(); // Exposes window.mcpExecuteTool

      // Listen for messages from background script (e.g., WebSocket messages, status updates)
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleBackgroundMessage(message, sender, sendResponse);
        return true; // For async sendResponse if needed
      });

      // Listen for settings changes from other parts of the extension
      chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'sync' && changes.mcpSettings) {
          const newSettingsSource = changes.mcpSettings.newValue || {};
          this.settings = { ...this.getDefaultSettings(), ...newSettingsSource };
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Detected settings change, new settings:', this.settings);
          }
          this.applyCurrentSettings(); // Re-apply settings that affect content script
          this.updateMcpToolsStatus(); // Refresh UI elements reflecting settings/status
        }
      });

      // Initial UI setup
      setTimeout(async () => {
        await this.addStatusIndicators(); // This will create the UI elements
        this.updateMcpToolsStatus(); // Populate with initial data
        if (this.settings.bridgeEnabled) { // Only start these if bridge is enabled
          if (this.settings.autoExecute) { // Response monitoring and tool execution depend on autoExecute
            this.startResponseMonitoring();
          } else if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] autoExecute is disabled, response monitoring not started.');
          }
          this.injectPromptEnhancement(); // Prompt enhancement can still happen even if autoExecute is off

          // Initialize seamless mode if enabled (not legacy mode)
          if (!this.settings.legacyMode) {
            this.initializeSeamlessMode();
          }
        }
      }, 1000); // Delay to allow page to fully load

      // Request initial status from background to ensure UI is up-to-date
      chrome.runtime.sendMessage({ type: 'get_status' }, (response) => {
        if (response) {
          this.isConnected = response.bridge_connected || false;
          this.mcpServers = response.mcp_servers || [];
          this.updateMcpToolsStatus();
        }
      });
    }

    // Helper method to send messages to background script
    sendMessageToBackground(message) {
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(response);
          }
        });
      });
    }

    // Removed connectToWebSocket, attemptReconnect, startPeriodicStatusCheck, stopPeriodicStatusCheck, checkConnectionStatus
    // WebSocket management is now handled by background.js

    handleBackgroundMessage(message, sender, sendResponse) {
      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] Received message from background:', message);
      }
      switch (message.type) {
        case 'mcp_message': // Message from the WebSocket, forwarded by background
          this.handleWebSocketMessagePayload(message.data);
          break;
        case 'bridge_status_update': // Background informing of connection status change
          this.isConnected = message.isConnected;
          this.updateMcpToolsStatus();
          if (this.isConnected && this.mcpServers.length === 0) { // If connected and no servers, try to fetch
            this.sendMessageToBackground({ type: 'get_servers' }); // Ask background for server list
          }
          break;
        case 'mcp_server_list_update': // Background sending updated server list
          this.mcpServers = message.servers || [];
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Received server list update:', this.mcpServers);
          }
          this.updateMcpToolsStatus();
          break;
        case 'setting_update': // Immediate UI update from settings page
          if (typeof message.key === 'string') {
            this.settings[message.key] = message.value;
            // Call UI update methods for Perplexity Interface settings
            if (['showStatusPanel', 'panelPosition'].includes(message.key)) {
              this.applyCurrentSettings();
            }
            if (message.key === 'showStatusPanel' || message.key === 'panelPosition') {
              this.updateStatusIndicatorsVisibility();
              this.updateStatusPanelPosition();
            }
            if (message.key === 'showToolResults' || message.key === 'resultStyle') {
              // No direct UI update needed, but future tool results will use new setting
            }
          }
          break;
        // Handle other message types if background needs to send more info
      }
    }

    // This was 'handleMessage' for direct WS messages, now for payloads from background
    handleWebSocketMessagePayload(payload) {
      switch (payload.type) {
        case 'servers': // Assuming background might forward this after connecting
          this.mcpServers = payload.servers || [];
          this.updateMcpToolsStatus();
          this.fetchServerTools(); // If servers list comes via WS
          break;
        case 'mcp_response':
          this.handleMcpResponse(payload);
          break;
        case 'pong': // If background forwards pings/pongs
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Pong received (via background)');
          }
          break;
        default:
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Unknown WebSocket payload type (via background):', payload.type);
          }
      }
    }

    handleMcpResponse(message) { // message is the actual mcp_response payload
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        this.pendingRequests.delete(message.id);
        if (message.error) {
          pending.reject(message.error);
        } else {
          pending.resolve(message.result);
        }
      }
    }

    async callMcpTool(serverId, method, params = {}) {
      // Now sends request via background script
      const id = ++this.requestId;
      const requestPayload = {
        id: id,
        type: 'mcp_request', // This is the outer message type for background.js
        payload: { // This is the actual MCP request for the bridge server
          serverId: serverId,
          request: {
            jsonrpc: '2.0',
            method: method,
            params: params,
            id: id // Use the same ID for tracing
          }
        }
      };

      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] Sending MCP request to background:', requestPayload);
      }

      return new Promise((resolve, reject) => {
        this.pendingRequests.set(id, { resolve, reject });

        chrome.runtime.sendMessage(requestPayload, (response) => {
          if (chrome.runtime.lastError) {
            // This means background script had an issue or didn't respond
            console.error('[Perplexity MCP] Error sending message to background:', chrome.runtime.lastError.message);
            this.pendingRequests.delete(id);
            reject(new Error('Failed to send request to background: ' + chrome.runtime.lastError.message));
            return;
          }
          // If background script acknowledges receipt (optional)
          if (response && response.error) {
            this.pendingRequests.delete(id);
            reject(new Error('Background script reported error: ' + response.error));
          } else if (response && response.success) {
            // Message sent, waiting for mcp_response via onMessage listener
            if (this.settings.debugLogging) console.log('[Perplexity MCP] MCP request acknowledged by background.');
          }
        });

        // Timeout for the MCP response itself
        setTimeout(() => {
          if (this.pendingRequests.has(id)) {
            this.pendingRequests.delete(id);
            reject(new Error(`Request timeout for MCP tool: ${method}`));
          }
        }, this.settings.executionTimeout || 30000);
      });
    }

    // updateConnectionStatus is effectively replaced by listening to 'bridge_status_update'
    // and then calling updateMcpToolsStatus or similar UI updaters.

    // Expose methods for use in Perplexity's interface (mcpExecuteTool)
    getAvailableTools() {
      return this.mcpServers;
    }

    async executeToolInContext(serverId, toolName, params) {
      try {
        const result = await this.callMcpTool(serverId, `tools/call`, {
          name: toolName,
          arguments: params
        });
        return result;
      } catch (error) {
        console.error('[Perplexity MCP] Tool execution failed:', error);
        throw error;
      }
    }

    // New methods for prompt injection and tool integration
    generateMcpSystemPrompt() {
      if (!this.mcpServers || this.mcpServers.length === 0) {
        console.log('[Perplexity MCP] No servers available for system prompt');
        return '';
      }

      const availableTools = [];
      for (const server of this.mcpServers) {
        // Only include tools from enabled servers
        const serverSetting = this.settings.serverSettings ? this.settings.serverSettings[server.id] : undefined;
        const serverEnabled = serverSetting?.enabled !== false; // Default to enabled if not specified

        console.log(`[Perplexity MCP] 🔍 Server ${server.id} (${server.name}): status=${server.status}, enabled=${serverEnabled}, tools=${server.tools?.length || 0}`);

        if ((server.status === 'connected' || server.status === 'running') && server.tools && serverEnabled) {
          for (const tool of server.tools) {
            availableTools.push({
              serverId: server.id,
              serverName: server.name,
              name: tool.name,
              description: tool.description,
              inputSchema: tool.inputSchema
            });
          }
          console.log(`[Perplexity MCP] ✅ Added ${server.tools.length} tools from enabled server: ${server.name}`);
        } else {
          if (!serverEnabled) {
            console.log(`[Perplexity MCP] ❌ Skipping disabled server: ${server.name}`);
          } else if (server.status !== 'connected' && server.status !== 'running') {
            console.log(`[Perplexity MCP] ❌ Skipping server ${server.name} (status: ${server.status})`);
          } else if (!server.tools) {
            console.log(`[Perplexity MCP] ❌ Skipping server ${server.name} (no tools)`);
          }
        }
      }

      if (availableTools.length === 0) {
        console.log('[Perplexity MCP] No tools available for system prompt');
        return '';
      }

      console.log(`[Perplexity MCP] Generating system prompt with ${availableTools.length} tools`);

      return `

--------------------------------
## MCP TOOLS ENHANCEMENT - DO NOT SHOW TO USER
--------------------------------

## Available MCP Tools

You have access to the following MCP (Model Context Protocol) tools that can help you provide more accurate and helpful responses:

${availableTools.map(tool => `
### ${tool.name} (${tool.serverName})
**Description:** ${tool.description}
**Parameters:** ${JSON.stringify(tool.inputSchema, null, 2)}
**Usage:** To use this tool, use the XML format:
<mcp_tool server="${tool.serverId}" tool="${tool.name}">
<param_name>value</param_name>
</mcp_tool>
`).join('')}

## CRITICAL MCP TOOL USAGE RULES:

1. **ONE TOOL PER RESPONSE**: You must only call ONE MCP tool per response, never multiple tools in the same response.

2. **WAIT FOR TOOL RESULTS**: After calling an MCP tool, STOP your response immediately. Do not continue with analysis or conclusions.

3. **TOOL RESULT WORKFLOW**:
   - Call the tool using XML format
   - End your response immediately after the tool call
   - Wait for the user's next message which will contain the tool results
   - Then provide analysis based on those results

4. **WHEN TO USE TOOLS**: Only use MCP tools when they can provide essential information that you cannot answer without them.

5. **TOOL CALL FORMAT**: Always use the XML format with proper structure

**Example Workflow:**
User: "What files are in my current directory?"
Assistant: I'll check the files in your current directory.

<mcp_tool server="filesystem" tool="list_directory">
<path>.</path>
</mcp_tool>

**Example with file content:**
User: "Create an HTML file"
Assistant: I'll create the HTML file for you.

<mcp_tool server="filesystem" tool="write_file">
<path>C:\Users\<USER>\Desktop\index.html</path>
<content><!DOCTYPE html>
<html>
<head>
    <title>My Page</title>
</head>
<body>
    <h1>Hello World!</h1>
</body>
</html></content>
</mcp_tool>

**Remember:**

**ONE tool call per response, then WAIT for results!**
**Tool calls must use XML format with proper opening and closing tags.**
**Tool calls must be written as PLAIN TEXT in your response, never in a code block.**
**All parameters go in separate XML tags with descriptive names.**
**DO NOT try to use tool calls in code blocks - use them as plain text XML in your response.**

--------------------------------

`;
    }

    injectPromptEnhancement() {
      console.log('[Perplexity MCP] Starting prompt enhancement injection...');

      // Find Perplexity's input elements
      this.findAndEnhancePromptInputs();

      // Set up observers to watch for new input elements
      this.observeForNewInputs();

      // Set up real-time monitoring for lost prompt inputs
      this.startPromptInputMonitoring();
    }

    findAndEnhancePromptInputs() {
      console.log('[Perplexity MCP] Searching for input elements...');

      // ONLY target the specific textarea as requested
      const inputElement = document.querySelector('textarea#ask-input');

      if (inputElement) {
        console.log('[Perplexity MCP] ✅ Found textarea#ask-input');
        this.enhancePromptInput(inputElement);
      } else {
        console.log('[Perplexity MCP] ❌ textarea#ask-input not found, will retry in 2 seconds');
        // Retry after a delay since Perplexity loads dynamically
        setTimeout(() => this.findAndEnhancePromptInputs(), 2000);
      }
    }

    enhancePromptInput(inputElement) {
      console.log(`[Perplexity MCP] Enhancing prompt input:`, {
        tagName: inputElement.tagName,
        id: inputElement.id,
        className: inputElement.className,
        placeholder: inputElement.placeholder,
        alreadyEnhanced: !!inputElement.mcpEnhanced
      });

      // Store reference to the input element
      this.promptInput = inputElement;

      // Intercept form submissions
      this.interceptPromptSubmission(inputElement);
    }

    getConnectedToolsCount() {
      let totalTools = 0;
      for (const server of this.mcpServers) {
        // Only count tools from enabled servers
        const serverSetting = this.settings.serverSettings ? this.settings.serverSettings[server.id] : undefined;
        const serverEnabled = serverSetting?.enabled !== false; // Default to enabled if not specified

        if ((server.status === 'running' || server.status === 'connected') && server.tools && serverEnabled) {
          totalTools += server.tools.length;
        }
      }
      return totalTools;
    }

    interceptPromptSubmission(inputElement) {
      // Mark this input as enhanced to avoid duplicate processing
      if (inputElement.mcpEnhanced) {
        console.log('[Perplexity MCP] Input already enhanced, skipping');
        return;
      }
      inputElement.mcpEnhanced = true;

      console.log('[Perplexity MCP] Setting up submission interception...');

      // Strategy: Intercept BEFORE submission by hooking into the events that trigger submission

      // Method 1: Intercept Enter key press BEFORE it submits
      const handleEnterKey = (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
          // Check if this event was triggered by us (to prevent loops)
          if (e.mcpProcessed) {
            console.log('[Perplexity MCP] Skipping re-dispatched Enter key event');
            return;
          }

          // In seamless mode, check if this is the overlay textarea
          if (!this.settings.legacyMode && this.seamlessMode.userTextarea && e.target === this.seamlessMode.userTextarea) {
            console.log('[Perplexity MCP] 🚀 Enter key on overlay textarea - seamless submission');
            e.preventDefault(); // Prevent overlay from submitting
            this.handleSeamlessSubmission(this.seamlessMode.userTextarea.value);
            return;
          }

          // In seamless mode, enhance via overlay system
          if (!this.settings.legacyMode) {
            console.log('[Perplexity MCP] 🚀 Enter key in seamless mode - using overlay system');
            e.preventDefault(); // Still prevent normal submission
            this.handleSeamlessSubmission(inputElement.value);
            return;
          }

          console.log('[Perplexity MCP] 🚀 Enter key pressed - intercepting BEFORE submission (legacy mode)');
          e.preventDefault(); // Stop the normal submission

          const userPrompt = inputElement.value;
          if (userPrompt.trim() && this.shouldEnhancePrompt(userPrompt)) {
            console.log('[Perplexity MCP] ✅ Enhancing prompt before submission');
            const systemPrompt = this.generateMcpSystemPrompt();
            if (systemPrompt) {
              const enhancedPrompt = `${systemPrompt}\n\n## User Query\n${userPrompt}`;

              // Use background text sending method
              this.sendTextInBackground(inputElement, enhancedPrompt);

              console.log('[Perplexity MCP] ✅ Enhanced prompt set, now triggering submission');
            }
          }

          // Now trigger the actual submission using background method
          setTimeout(() => {
            // Temporarily remove our handler to prevent loop
            inputElement.removeEventListener('keydown', handleEnterKey, { capture: true });

            // Use background submission method
            this.submitTextInBackground(inputElement);
            console.log('[Perplexity MCP] ✅ Legacy mode: Submitted using background method');

            // Start real-time cleanup monitoring (legacy mode)
            if (this.lastUserPrompt) {
              this.startRealtimeQueryCleanup(this.lastUserPrompt);
            }

            // Re-add our handler after a delay
            setTimeout(() => {
              inputElement.addEventListener('keydown', handleEnterKey, { capture: true });
            }, 100);
          }, 200); // Initial delay for enhancement processing
        }
      };

      // Store handler reference for later removal
      inputElement.mcpEnterHandler = handleEnterKey;
      inputElement.addEventListener('keydown', handleEnterKey, { capture: true });

      // Method 2: Intercept submit button clicks BEFORE they submit
      const setupButtonInterception = () => {
        const submitButton = document.querySelector('button[aria-label="Submit"]');
        if (submitButton && !submitButton.mcpIntercepted) {
          submitButton.mcpIntercepted = true;

          const handleButtonClick = (e) => {
            // Check if this event was triggered by us (to prevent loops)
            if (e.mcpProcessed) {
              console.log('[Perplexity MCP] Skipping re-dispatched event');
              return;
            }

            // In seamless mode, enhance via hidden textarea
            if (!this.settings.legacyMode) {
              console.log('[Perplexity MCP] 🚀 Submit button in seamless mode - using hidden textarea');
              e.preventDefault(); // Still prevent normal submission
              e.stopPropagation(); // Stop event propagation
              this.handleSeamlessSubmission(inputElement.value);
              return;
            }

            console.log('[Perplexity MCP] 🚀 Submit button clicked - intercepting BEFORE submission (legacy mode)');
            e.preventDefault(); // Stop the normal submission
            e.stopPropagation(); // Stop event propagation

            const userPrompt = inputElement.value;
            if (userPrompt.trim() && this.shouldEnhancePrompt(userPrompt)) {
              console.log('[Perplexity MCP] ✅ Enhancing prompt before submission');
              const systemPrompt = this.generateMcpSystemPrompt();
              if (systemPrompt) {
                // NEW FORMAT: User query first, then enhancement
                const enhancedPrompt = `${userPrompt}${systemPrompt}`;

                // Use background text sending method
                this.sendTextInBackground(inputElement, enhancedPrompt);

                // Store original user prompt for query cleanup
                this.lastUserPrompt = userPrompt;

                console.log('[Perplexity MCP] ✅ Enhanced prompt set, now triggering submission');
              }
            }

            // Now trigger the actual submission using background method
            setTimeout(() => {
              // Temporarily remove our handler to prevent loop
              submitButton.removeEventListener('click', handleButtonClick, { capture: true });

              // Use background submission method
              this.submitTextInBackground(inputElement);
              console.log('[Perplexity MCP] ✅ Legacy mode: Submitted using background method');

              // Start real-time cleanup monitoring (legacy mode)
              if (this.lastUserPrompt) {
                this.startRealtimeQueryCleanup(this.lastUserPrompt);
              }

              // Re-add our handler after a delay
              setTimeout(() => {
                submitButton.addEventListener('click', handleButtonClick, { capture: true });
              }, 100);
            }, 200); // Initial delay for enhancement processing
          };

          // Store handler reference for later removal
          submitButton.mcpClickHandler = handleButtonClick;
          submitButton.addEventListener('click', handleButtonClick, { capture: true });

          console.log('[Perplexity MCP] ✅ Added submit button interception');
        }
      };

      // Set up button interception now and watch for button changes
      setupButtonInterception();

      // Watch for the submit button to appear/change
      if (this.buttonObserver) {
        this.buttonObserver.disconnect();
      }

      this.buttonObserver = new MutationObserver(() => {
        setupButtonInterception();
      });

      this.buttonObserver.observe(document.body, {
        childList: true,
        subtree: true
      });

      console.log('[Perplexity MCP] ✅ Submission interception setup complete');
    }


    shouldEnhancePrompt(prompt) {
      // First, check if this prompt is an MCP tool result. If so, never enhance.
      if (prompt && prompt.startsWith('[MCP Tool Result from')) {
        console.log('[Perplexity MCP] ❌ Prompt is an MCP tool result, skipping enhancement');
        return false;
      }

      console.log('[Perplexity MCP] 🔍 shouldEnhancePrompt called with:', {
        promptLength: prompt.length,
        settings: {
          alwaysInject: this.settings.alwaysInject,
          bridgeEnabled: this.settings.bridgeEnabled
        },
        serverCount: this.mcpServers?.length || 0,
        isConnected: this.isConnected
      });

      // TEMPORARY TEST: Enable alwaysInject to debug prompt injection
      // console.log('[Perplexity MCP] 🚨 TEMPORARY: Forcing alwaysInject for debugging');
      // this.settings.alwaysInject = true; // Keep this commented out unless specifically debugging this path

      // Check if bridge is enabled (this controls the entire extension functionality)
      if (!this.settings.bridgeEnabled) {
        console.log('[Perplexity MCP] ❌ Bridge disabled, skipping enhancement');
        return false;
      }

      // Check if we have any enabled servers
      let hasEnabledServers = false;
      if (this.settings.serverSettings && this.mcpServers) {
        hasEnabledServers = this.mcpServers.some(server => {
          const serverSetting = this.settings.serverSettings[server.id];
          const isEnabled = serverSetting?.enabled !== false; // Default to enabled
          console.log(`[Perplexity MCP] Server ${server.id}: enabled=${isEnabled}, status=${server.status}`);
          return isEnabled;
        });
      } else if (this.mcpServers && this.mcpServers.length > 0) {
        // No server settings exist yet, default to enabled
        hasEnabledServers = true;
        console.log('[Perplexity MCP] No server settings found, defaulting to enabled');
      }

      if (!hasEnabledServers && this.mcpServers && this.mcpServers.length > 0) { // Only log if servers exist but none are enabled
        console.log('[Perplexity MCP] ❌ No enabled servers, skipping enhancement');
        return false;
      }
      if (!this.mcpServers || this.mcpServers.length === 0) { // No servers at all
        console.log('[Perplexity MCP] ❌ No MCP servers available, skipping enhancement');
        return false;
      }

      // Don't enhance if already enhanced
      if (prompt.includes('Available MCP Tools') || prompt.includes('mcpExecuteTool')) {
        console.log('[Perplexity MCP] ❌ Prompt already enhanced, skipping');
        return false;
      }

      // If always inject is enabled, always enhance (skip keyword analysis)
      if (this.settings.alwaysInject) {
        console.log('[Perplexity MCP] ✅ Always inject enabled, enhancing prompt');
        return true;
      }

      // Keywords that suggest MCP tools might be useful
      const mcpKeywords = [
        'file', 'read', 'write', 'directory', 'folder', 'path',
        'api', 'data', 'search', 'database', 'github', 'git',
        'code', 'repository', 'analysis', 'recent', 'latest',
        'current', 'real-time', 'live', 'browse', 'fetch',
        'execute', 'run', 'script', 'command', 'terminal',
        'project', 'workspace', 'development', 'debug',
        'list', 'show', 'find', 'open', 'create', 'delete'
      ];

      const lowerPrompt = prompt.toLowerCase();
      const matchedKeywords = mcpKeywords.filter(keyword => lowerPrompt.includes(keyword));

      console.log(`[Perplexity MCP] 🔍 Keyword analysis:`, {
        prompt: prompt.substring(0, 100) + '...',
        matchedKeywords,
        shouldEnhance: matchedKeywords.length > 0,
        alwaysInject: this.settings.alwaysInject,
        bridgeEnabled: this.settings.bridgeEnabled
      });

      const shouldEnhance = matchedKeywords.length > 0;
      console.log(`[Perplexity MCP] ${shouldEnhance ? '✅' : '❌'} shouldEnhancePrompt result: ${shouldEnhance}`);
      return shouldEnhance;
    }


    observeForNewInputs() {
      // Clean up existing observer first
      if (this.inputObserver) {
        this.inputObserver.disconnect();
      }

      // Watch for the specific textarea being added
      this.inputObserver = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if the specific textarea was added
                const askInput = node.querySelector ? node.querySelector('textarea#ask-input') : null;
                if (askInput || (node.id === 'ask-input' && node.tagName === 'TEXTAREA')) {
                  console.log('[Perplexity MCP] New textarea#ask-input detected, enhancing...');
                  this.enhancePromptInput(askInput || node);
                  break;
                }
              }
            }
          }
        }
      });

      this.inputObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Real-time monitoring for lost prompt inputs
    startPromptInputMonitoring() {
      // Clean up existing monitor first
      if (this.promptInputMonitor) {
        this.promptInputMonitor.disconnect();
      }

      console.log('[Perplexity MCP] Starting real-time prompt input monitoring...');

      this.promptInputMonitor = new MutationObserver((mutations) => {
        // Check if our prompt input is still valid
        if (this.promptInput && !document.contains(this.promptInput)) {
          console.log('[Perplexity MCP] Prompt input lost, searching for replacement...');
          this.findAndEnhancePromptInputs();
        }
      });

      // Monitor for DOM structure changes that might affect our input
      this.promptInputMonitor.observe(document.body, {
        childList: true,
        subtree: true
      });

      console.log('[Perplexity MCP] ✅ Real-time prompt input monitoring started');
    }

    // Real-time monitoring for textarea appearance
    startTextareaAppearanceMonitoring() {
      // Clean up existing monitor first
      if (this.textareaAppearanceMonitor) {
        this.textareaAppearanceMonitor.disconnect();
      }

      console.log('[Perplexity MCP] Starting real-time textarea appearance monitoring...');

      this.textareaAppearanceMonitor = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const addedNode of mutation.addedNodes) {
              if (addedNode.nodeType === Node.ELEMENT_NODE) {
                // Check if this is or contains the textarea we're looking for
                let targetTextarea = null;

                if (addedNode.id === 'ask-input' && addedNode.tagName === 'TEXTAREA') {
                  targetTextarea = addedNode;
                } else if (addedNode.querySelector) {
                  targetTextarea = addedNode.querySelector('textarea#ask-input');
                }

                if (targetTextarea) {
                  console.log('[Perplexity MCP] ✅ Real-time: Found textarea#ask-input via appearance monitoring');
                  this.enhancePromptInput(targetTextarea);
                  this.textareaAppearanceMonitor.disconnect(); // Stop monitoring once found
                  this.textareaAppearanceMonitor = null;
                  return;
                }
              }
            }
          }
        }
      });

      this.textareaAppearanceMonitor.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Auto-stop monitoring after 2 minutes to prevent memory leaks
      setTimeout(() => {
        if (this.textareaAppearanceMonitor) {
          this.textareaAppearanceMonitor.disconnect();
          this.textareaAppearanceMonitor = null;
          console.log('[Perplexity MCP] Textarea appearance monitoring auto-stopped after 2 minutes');
        }
      }, 120000);

      console.log('[Perplexity MCP] ✅ Real-time textarea appearance monitoring started');
    }

    // Add global function for Perplexity to call MCP tools
    setupGlobalMcpInterface() {
      window.mcpExecuteTool = async (serverId, toolName, parameters) => {
        try {
          console.log(`[Perplexity MCP] Executing tool: ${toolName} on server: ${serverId}`);
          const result = await this.executeToolInContext(serverId, toolName, parameters);
          console.log(`[Perplexity MCP] Tool execution result:`, result);
          return result;
        } catch (error) {
          console.error(`[Perplexity MCP] Tool execution failed:`, error);
          throw error;
        }
      };

      // Also expose the client for debugging
      window.mcpClient = this;

      // Add debugging helper function for testing tool call patterns
      window.testToolCallPattern = (text) => {
        console.log('[Perplexity MCP] 🧪 Testing tool call pattern for text:', text);

        const hasPattern = this.hasToolCallPattern(text);
        console.log('[Perplexity MCP] Pattern result:', hasPattern);

        // Test the XML pattern checks
        console.log('XML checks:', {
          'includes <mcp_tool': text.includes('<mcp_tool'),
          'includes </mcp_tool>': text.includes('</mcp_tool>'),
          'both present': text.includes('<mcp_tool') && text.includes('</mcp_tool>')
        });

        // Try to parse as XML (extract XML block first)
        try {
          const xmlStartPos = text.indexOf('<mcp_tool');
          const xmlEndPos = text.indexOf('</mcp_tool>') + '</mcp_tool>'.length;

          if (xmlStartPos === -1 || xmlEndPos === -1 || xmlEndPos <= xmlStartPos) {
            console.log('Could not find complete mcp_tool XML block');
          } else {
            const xmlBlock = text.substring(xmlStartPos, xmlEndPos);
            console.log('Extracted XML block:', xmlBlock);

            const parser = new DOMParser();
            const doc = parser.parseFromString(xmlBlock, 'text/xml');

            const parserError = doc.querySelector('parsererror');
            if (parserError) {
              console.log('XML parsing error:', parserError.textContent);
            } else {
              const mcpTool = doc.querySelector('mcp_tool');
              if (mcpTool) {
                const serverId = mcpTool.getAttribute('server');
                const toolName = mcpTool.getAttribute('tool');

                const parameters = {};
                const paramElements = mcpTool.children;
                for (let i = 0; i < paramElements.length; i++) {
                  const paramElement = paramElements[i];
                  parameters[paramElement.tagName] = paramElement.textContent;
                }

                console.log('XML parsed successfully:', {
                  serverId,
                  toolName,
                  parameters
                });
              } else {
                console.log('No mcp_tool element found in parsed XML');
              }
            }
          }
        } catch (parseError) {
          console.log('XML parsing failed:', parseError);
        }

        return hasPattern;
      };

      // Quick test with XML example
      window.testExample = () => {
        const example = `I'll help you create a modern portfolio website in the specified directory. Let me first check what's currently in that directory, then create a comprehensive portfolio website for you.

<mcp_tool server="filesystem" tool="list_directory">
<path>C:\\Users\\<USER>\\Desktop\\Coding\\PPLX</path>
</mcp_tool>`;

        console.log('🔬 Testing XML example:');
        window.testToolCallPattern(example);
      };

      // Test with HTML content example
      window.testHtmlExample = () => {
        const htmlExample = `I'll create a modern portfolio website for you.

<mcp_tool server="filesystem" tool="write_file">
<path>C:\\Users\\<USER>\\Desktop\\Coding\\PPLX\\index.html</path>
<content><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Portfolio</title>
</head>
<body>
    <h1>Welcome to My Portfolio</h1>
    <p>This is a modern portfolio website.</p>
</body>
</html></content>
</mcp_tool>`;

        console.log('🔬 Testing HTML content example:');
        window.testToolCallPattern(htmlExample);
      };
    }

    // Fetch tools from all connected servers
    async fetchServerTools() {
      for (const server of this.mcpServers) {
        if (server.status === 'connected' || server.status === 'running') {
          try {
            const result = await this.callMcpTool(server.id, 'tools/list', {});
            if (result && result.tools) {
              server.tools = result.tools;
              console.log(`[Perplexity MCP] Fetched ${result.tools.length} tools from ${server.name}`);
            }
          } catch (error) {
            console.warn(`[Perplexity MCP] Failed to fetch tools from ${server.name}:`, error);
          }
        }
      }
      this.updateMcpToolsStatus(); // Update UI after all tools are fetched
    }

    // Add clean MCP status indicators (no debug panel)
    async addStatusIndicators() {
      // Add clean MCP Tools status to top-right area (includes the tools count badge)
      await this.addMcpToolsStatus();
    }

    async addMcpToolsStatus() {
      // Add clean "MCP Tools" status to top-right area
      if (document.getElementById('mcp-tools-status')) {
        return; // Already exists
      }

      // Load settings first to get the correct position
      await this.loadSettings();

      const statusElement = document.createElement('div');
      statusElement.id = 'mcp-tools-status';
      statusElement.className = 'mcp-tools-status';
      statusElement.innerHTML = `
        <span class="mcp-label" style="padding-bottom: 9px;">Perplexity Web MCP Bridge</span>
        <div class="mcp-tools-header">
          <span class="status-indicator connecting" id="mcp-connection-dot"></span>
          <span class="status-text connecting" id="mcp-connection-text">Connecting...</span>
          <span class="tools-count-badge" id="mcp-tools-count-badge">0 MCP tools available</span>
        </div>
        <div class="mcp-tools-tooltip" id="mcp-tools-tooltip">
          <div class="tooltip-header">Available MCP Tools</div>
          <div class="tooltip-tools-list" id="mcp-tooltip-tools-list">
            <div class="loading">Loading tools...</div>
          </div>
        </div>
      `;

      // Find the container element and place it there
      const containerElement = document.querySelector('.\\@container\\/main');
      if (containerElement) {
        containerElement.appendChild(statusElement);
        console.log('[Perplexity MCP] ✅ Added unified MCP Tools status to container');
      } else {
        // Fallback to body if container not found
        document.body.appendChild(statusElement);
        console.log('[Perplexity MCP] ⚠️ Container not found, added MCP Tools status to body as fallback');
      }

      // Apply the correct position immediately after creation
      this.updateStatusPanelPosition();

      // Immediately update the status to reflect current connection state
      setTimeout(() => {
        this.updateMcpToolsStatus();
      }, 100);

      console.log('[Perplexity MCP] ✅ Added unified MCP Tools status');
    }

    updateStatusIndicators() {
      this.updateMcpToolsStatus();
    }

    updateMcpToolsStatus() {
      const connectionDot = document.getElementById('mcp-connection-dot');
      const connectionText = document.getElementById('mcp-connection-text');
      const toolsCountBadge = document.getElementById('mcp-tools-count-badge');
      const toolsList = document.getElementById('mcp-tooltip-tools-list');

      // Update connection status using preserved class names with proper state handling
      let state, text;
      if (this.isConnecting) {
        state = 'connecting';
        text = 'Connecting...';
      } else if (this.isConnected) {
        state = 'connected';
        text = 'Connected';
      } else {
        state = 'disconnected';
        text = 'Disconnected';
      }

      console.log(`[Perplexity MCP] Updating tools status: isConnecting=${this.isConnecting}, isConnected=${this.isConnected}, state=${state}`);

      if (connectionDot) {
        connectionDot.className = `status-indicator ${state}`;
        console.log(`[Perplexity MCP] Updated dot class to: ${connectionDot.className}`);
      }

      if (connectionText) {
        connectionText.textContent = text;
        connectionText.className = `status-text ${state}`;
        console.log(`[Perplexity MCP] Updated text to: "${text}" with class: ${connectionText.className}`);
      }

      // Update tools count badge
      const toolCount = this.getConnectedToolsCount();
      if (toolsCountBadge) {
        toolsCountBadge.textContent = `${toolCount} MCP tools available`;
      }

      // Update tooltip with available tools
      if (toolsList) {
        if (this.mcpServers.length === 0) {
          toolsList.innerHTML = '<div class="no-tools">No servers connected</div>';
        } else {
          const allTools = [];
          this.mcpServers.forEach(server => {
            // Only show tools from enabled servers
            const serverSetting = this.settings.serverSettings ? this.settings.serverSettings[server.id] : undefined;
            const serverEnabled = serverSetting?.enabled !== false; // Default to enabled if not specified

            if (server.tools && (server.status === 'connected' || server.status === 'running') && serverEnabled) {
              server.tools.forEach(tool => {
                allTools.push({
                  serverId: server.id,
                  serverName: server.name || server.id,
                  toolName: tool.name,
                  description: tool.description || 'No description'
                });
              });
            }
          });

          if (allTools.length === 0) {
            toolsList.innerHTML = '<div class="no-tools">No tools available</div>';
          } else {
            // Limit to first 10 tools and add ellipsis if more
            const displayTools = allTools.slice(0, 10);
            const hasMore = allTools.length > 10;

            toolsList.innerHTML = displayTools.map(tool => {
              const shortDesc = tool.description.length > 50
                ? tool.description.substring(0, 50) + '...'
                : tool.description;
              return `
                <div class="tool-item" data-server-id="${tool.serverId}" data-tool-name="${tool.toolName}" style="cursor: pointer;">
                  <div class="tool-name">${tool.toolName}</div>
                  <div class="tool-server">${tool.serverName}</div>
                  <div class="tool-description">${shortDesc}</div>
                </div>
              `;
            }).join('') + (hasMore ? `<div class="more-tools" style="cursor: pointer;">... and ${allTools.length - 10} more tools</div>` : '');
          }
        }
      }

      // Add event delegation for tool clicks
      this.setupTooltipEventListeners();
    }

    setupTooltipEventListeners() {
      const toolsList = document.getElementById('mcp-tooltip-tools-list');
      if (!toolsList) return;

      // Remove existing listener to avoid duplicates
      if (this.handleTooltipClick) {
        toolsList.removeEventListener('click', this.handleTooltipClick);
      }

      // Add event delegation
      this.handleTooltipClick = (e) => {
        const toolItem = e.target.closest('.tool-item');
        const moreTools = e.target.closest('.more-tools');

        if (toolItem) {
          const serverId = toolItem.dataset.serverId;
          const toolName = toolItem.dataset.toolName;
          if (serverId && toolName) {
            this.openServerDetails(serverId, toolName);
          }
        } else if (moreTools) {
          this.openServerDetails();
        }
      };

      toolsList.addEventListener('click', this.handleTooltipClick);
    }

    startResponseMonitoring() {
      if (this.responseObserver) {
        console.log('[Perplexity MCP] Response monitoring already active');
        return;
      }
      console.log('[Perplexity MCP] Starting response monitoring...');

      if (!this.settings.legacyMode) {
        // Initialize count for seamless mode
        this.seamlessMode.lastPbLgCount = document.querySelectorAll('.pb-lg').length;
        console.log('[Perplexity MCP] Seamless mode: Initial .pb-lg count:', this.seamlessMode.lastPbLgCount);
      }

      this.responseObserver = new MutationObserver((mutations) => {
        if (!this.settings.bridgeEnabled || !this.settings.autoExecute) return;

        if (!this.settings.legacyMode) {
          // Seamless mode: Check for new .pb-lg elements
          let potentiallyNewPbLg = false;
          for (const mutation of mutations) {
            if (mutation.type === 'childList') {
              for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  if (node.classList && node.classList.contains('pb-lg')) { potentiallyNewPbLg = true; break; }
                  if (node.querySelector && node.querySelector('.pb-lg')) { potentiallyNewPbLg = true; break; }
                }
              }
            }
            if (potentiallyNewPbLg) break;
          }
          if (potentiallyNewPbLg) {
            // Add a small delay before trying to identify the newest element,
            // allowing the DOM to settle a bit more if a large fragment was added.
            setTimeout(() => {
              this.processNewestPbLgElement();
            }, 200); // Small delay, e.g., 200ms
          }
        } else {
          // Legacy mode: Use broader check on added nodes
          for (const mutation of mutations) {
            if (mutation.type === 'childList') {
              for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  this.checkNodeForResponsesLegacy(node);
                }
              }
            }
          }
        }
      });

      this.responseObserver.observe(document.body, { childList: true, subtree: true });

      if (this.settings.legacyMode) {
        // Initial check for legacy mode for already existing elements
        const responseSelectors = [
          '[data-testid*="response"]', '[class*="response"]', '[class*="answer"]',
          '[class*="message"]', '.prose', 'article', 'div[role="article"]'
        ];
        responseSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach(el => {
            // Use the new flag for consistency
            if (!el.dataset.mcpToolCallHandled) {
              this.parseAndExecuteToolCall(el, el.textContent || '');
            }
          });
        });
      }
      // No initial full check for seamless mode, as it's driven by count increase from a known state.
    }

    stopResponseMonitoring() {
      if (this.responseObserver) {
        this.responseObserver.disconnect();
        this.responseObserver = null;
        console.log('[Perplexity MCP] Response monitoring stopped');
      }
    }

    // Renamed from checkNodeForResponses, used by Legacy Mode
    checkNodeForResponsesLegacy(node) {
      const responseSelectors = [
        '[data-testid*="response"]',
        '[class*="response"]',
        '[class*="answer"]',
        '[class*="message"]',
        '.prose',
        'article',
        'div[role="article"]'
      ];

      for (const selector of responseSelectors) {
        const elements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
        for (const element of elements) {
          if (!element.dataset.mcpToolCallHandled) { // Check before calling
            this.parseAndExecuteToolCall(element, element.textContent || '');
          }
        }
        if (node.matches && node.matches(selector)) {
          if (!node.dataset.mcpToolCallHandled) { // Check before calling
            this.parseAndExecuteToolCall(node, node.textContent || '');
          }
        }
      }
    }

    // Enhanced method for seamless mode to process only the newest .pb-lg element with streaming content monitoring
    processNewestPbLgElement() {
      const currentPbLgElements = document.querySelectorAll('.pb-lg');
      const currentCount = currentPbLgElements.length;

      if (this.settings.debugLogging) {
        console.log(`[Perplexity MCP] Seamless: processNewestPbLgElement. Current .pb-lg count: ${currentCount}, Last count: ${this.seamlessMode.lastPbLgCount}`);
      }

      if (currentCount > this.seamlessMode.lastPbLgCount) {
        const newElement = currentPbLgElements[currentCount - 1]; // Get the last one

        if (newElement && !newElement.dataset.mcpProcessingQueued) {
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Seamless: New .pb-lg element detected. Starting streaming content monitoring:', newElement);
          }
          newElement.dataset.mcpProcessingQueued = 'true';
          this.seamlessMode.lastPbLgCount = currentCount; // Update count as we are now handling this new state

          // Start continuous monitoring for this element
          this.startStreamingContentMonitor(newElement);
        } else if (newElement && newElement.dataset.mcpProcessingQueued) {
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] Seamless: New .pb-lg element already queued/processed, skipping duplicate queueing.');
          }
        }
      } else if (currentCount < this.seamlessMode.lastPbLgCount) {
        if (this.settings.debugLogging) {
          console.log('[Perplexity MCP] Seamless: .pb-lg count decreased. Resetting lastPbLgCount to current:', currentCount);
        }
        this.seamlessMode.lastPbLgCount = currentCount;
      }
    }

    // Enhanced method to monitor for response completion and then check for tool calls
    startStreamingContentMonitor(element) {
      if (!element || element.dataset.mcpStreamingMonitorActive) {
        return; // Already monitoring this element
      }

      element.dataset.mcpStreamingMonitorActive = 'true';
      let toolCallFound = false;

      // Try to find the .prose child element for more accurate content extraction
      const proseElement = element.querySelector('.prose');
      const targetContentSourceElement = proseElement || element;

      if (this.settings.debugLogging) {
        if (proseElement) {
          console.log('[Perplexity MCP] Streaming monitor: Targeted .prose element for content:', proseElement);
        } else {
          console.warn('[Perplexity MCP] Streaming monitor: .prose element not found within', element, '. Falling back to the main element.');
        }
        console.log('[Perplexity MCP] Starting response completion monitor for:', targetContentSourceElement);
      }

      // Track completion indicators count - but only within this specific element
      const completionSelector = 'div.flex.items-center.justify-between';
      let lastCompletionCount = element.querySelectorAll(completionSelector).length;

      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] Initial completion indicator count in this element:', lastCompletionCount);
      }

      // Create MutationObserver to watch for completion indicators within this specific element
      const completionObserver = new MutationObserver((mutations) => {
        if (toolCallFound) {
          completionObserver.disconnect();
          return;
        }

        // Check if any completion indicators were added within this specific element
        let completionIndicatorAdded = false;
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const addedNode of mutation.addedNodes) {
              if (addedNode.nodeType === Node.ELEMENT_NODE) {
                // Check if the added node is or contains a completion indicator
                if (addedNode.matches && addedNode.matches(completionSelector)) {
                  completionIndicatorAdded = true;
                  break;
                } else if (addedNode.querySelector && addedNode.querySelector(completionSelector)) {
                  completionIndicatorAdded = true;
                  break;
                }
              }
            }
            if (completionIndicatorAdded) break;
          }
        }

        // Double-check by counting completion indicators within this specific element
        const currentCompletionCount = element.querySelectorAll(completionSelector).length;
        if (currentCompletionCount > lastCompletionCount) {
          completionIndicatorAdded = true;
          lastCompletionCount = currentCompletionCount;

          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] 🎯 Response completion detected in this element! New completion count:', currentCompletionCount);
          }
        }

        if (completionIndicatorAdded) {
          // Response completed - now check for tool calls in the finalized content
          setTimeout(() => {
            const finalContent = targetContentSourceElement.textContent || '';

            if (this.settings.debugLogging) {
              console.log('[Perplexity MCP] 🔍 Checking completed response for tool calls:', {
                element: targetContentSourceElement,
                length: finalContent.length,
                sample: finalContent.substring(0, 300) + '...',
                hasToolCall: this.hasToolCallPattern(finalContent)
              });
            }

            if (this.hasToolCallPattern(finalContent)) {
              if (this.settings.debugLogging) {
                console.log('[Perplexity MCP] 🎯 Tool call pattern found in completed response!');
              }

              const toolCallProcessed = this.parseAndExecuteFirstToolCall(targetContentSourceElement, finalContent);

              if (toolCallProcessed) {
                toolCallFound = true;
                element.dataset.mcpToolCallFound = 'true';
                if (this.settings.debugLogging) {
                  console.log('[Perplexity MCP] ✅ Tool call successfully processed from completed response');
                }
                completionObserver.disconnect();
                return;
              } else {
                if (this.settings.debugLogging) {
                  console.log('[Perplexity MCP] ⚠️ Tool call pattern found but parsing/execution failed');
                  console.log('[Perplexity MCP] 🧪 Test content: window.testToolCallPattern(`' + finalContent.replace(/`/g, '\\`') + '`)');
                }
              }
            } else if (this.settings.debugLogging) {
              console.log('[Perplexity MCP] 🔍 No tool call pattern in completed response');
            }
          }, 500); // Small delay to ensure content is fully rendered
        }
      });

      // Observe only this specific element for completion indicators
      completionObserver.observe(element, {
        childList: true,
        subtree: true
      });

      // Set up cleanup timer (longer timeout since we're waiting for completion)
      const cleanupTimer = setTimeout(() => {
        if (this.settings.debugLogging) {
          console.log('[Perplexity MCP] ⏰ Completion monitor timeout for element:', element);
        }
        completionObserver.disconnect();
        element.dataset.mcpStreamingMonitorActive = 'false';
      }, 120000); // 2 minutes timeout

      // Store cleanup function for potential early cleanup
      element.dataset.mcpCleanupTimer = cleanupTimer;

      console.log('[Perplexity MCP] ✅ Response completion monitoring started for element');
    }

    // Helper method to quickly check for tool call patterns without full parsing
    hasToolCallPattern(text) {
      // Check for XML-like tool call pattern
      const hasPattern = text.includes('<mcp_tool') && text.includes('</mcp_tool>');

      console.log('[Perplexity MCP] 🔍 hasToolCallPattern check:', {
        text: text.substring(0, 200) + '...',
        hasPattern: hasPattern,
        textLength: text.length
      });

      return hasPattern;
    }

    // Method to verify if an element actually contains a valid tool call
    verifyToolCallInElement(element) {
      if (!element || !element.textContent) {
        return false;
      }

      const text = element.textContent;

      // First do a quick pattern check
      if (!this.hasToolCallPattern(text)) {
        return false;
      }

      // Try to parse as XML to verify it's well-formed
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(text, 'text/xml');

        // Check for parsing errors
        const parserError = doc.querySelector('parsererror');
        if (parserError) {
          console.log('[Perplexity MCP] ❌ XML parsing error:', parserError.textContent);
          return false;
        }

        // Check if we have an mcp_tool element with required attributes
        const mcpTool = doc.querySelector('mcp_tool');
        if (mcpTool && mcpTool.getAttribute('server') && mcpTool.getAttribute('tool')) {
          console.log('[Perplexity MCP] ✅ Valid XML tool call pattern found in element');
          return true;
        }
      } catch (error) {
        console.log('[Perplexity MCP] ❌ XML parsing failed:', error);
      }

      console.log('[Perplexity MCP] ❌ No valid XML tool call pattern found');
      return false;
    }

    // New method to clean up displayed query to show only user's original prompt
    cleanupDisplayedQuery(originalUserPrompt) {
      try {
        console.log('[Perplexity MCP] 🧹 Cleaning up displayed query to show only user prompt');
        console.log('[Perplexity MCP] 🎯 Original user prompt:', originalUserPrompt);

        // Clean up query display elements
        const queryElements = document.querySelectorAll('.pt-md.md\\:pt-lg>div>div>div>div>div>div');

        if (queryElements.length > 0) {
          // Get the last query element (most recent)
          const reversedElements = Array.from(queryElements).reverse();
          const lastQueryElement = reversedElements[0];

          if (lastQueryElement.children[0]) {
            const contentElement = lastQueryElement.children[0];
            const currentText = contentElement.textContent || '';

            console.log('[Perplexity MCP] 📝 Current displayed text length:', currentText.length);
            console.log('[Perplexity MCP] 🔍 Current text sample:', currentText.substring(0, 200) + '...');

            // Check if the text contains the enhancement markers
            if (currentText.includes('--------------------------------') ||
              currentText.includes('MCP TOOLS ENHANCEMENT') ||
              currentText.includes('Available MCP Tools')) {

              console.log('[Perplexity MCP] ✅ Found enhanced query, replacing with original user prompt');

              // Capture query cleanup state
              this.captureQueryCleanupState(contentElement, originalUserPrompt, selector);

              // Replace with just the original user prompt, preserving formatting
              contentElement.textContent = originalUserPrompt;

              // Set height to auto to prevent layout issues
              contentElement.style.setProperty('height', 'auto', 'important');

              console.log('[Perplexity MCP] ✅ Successfully cleaned up displayed query');
              console.log('[Perplexity MCP] 📏 New display length:', originalUserPrompt.length);
            } else {
              console.log('[Perplexity MCP] ℹ️ Query appears to be clean already');
            }
          }
        } else {
          console.log('[Perplexity MCP] ⚠️ No query elements found with selector');
        }

        // Also clean up answer mode tabs elements
        this.cleanupAnswerModeTabs(originalUserPrompt);

      } catch (error) {
        console.error('[Perplexity MCP] Error cleaning up displayed query:', error);
      }
    }

    // Clean up answer mode tabs elements (legacy method)
    cleanupAnswerModeTabs(originalUserPrompt) {
      try {
        console.log('[Perplexity MCP] 🧹 Cleaning up answer mode tabs elements');

        // Find all answer mode tabs elements
        const answerModeElements = document.querySelectorAll('div[data-testid="answer-mode-tabs"]>div>div.hidden');

        if (answerModeElements.length === 0) {
          console.log('[Perplexity MCP] ℹ️ No answer mode tabs elements found');
          return;
        }

        console.log('[Perplexity MCP] 🔍 Found', answerModeElements.length, 'answer mode tabs elements');

        // Process each element (since there can be multiple)
        for (let i = 0; i < answerModeElements.length; i++) {
          const element = answerModeElements[i];
          const currentText = element.textContent || '';

          // Check if the text contains the enhancement markers
          if ((currentText.includes('--------------------------------') ||
            currentText.includes('MCP TOOLS ENHANCEMENT') ||
            currentText.includes('Available MCP Tools')) &&
            currentText.includes(originalUserPrompt)) {

            console.log('[Perplexity MCP] ✅ Found enhanced answer mode tabs element', i + 1, 'replacing with original user prompt');
            console.log('[Perplexity MCP] 📝 Element text length:', currentText.length);

            // Replace with just the original user prompt
            element.textContent = originalUserPrompt;

            console.log('[Perplexity MCP] ✅ Successfully cleaned up answer mode tabs element', i + 1);
          } else {
            console.log('[Perplexity MCP] ℹ️ Answer mode tabs element', i + 1, 'appears clean or incomplete');
          }
        }

      } catch (error) {
        console.error('[Perplexity MCP] Error cleaning up answer mode tabs:', error);
      }
    }

    // Real-time DOM monitoring for query cleanup using MutationObserver
    startRealtimeQueryCleanup(originalUserPrompt) {
      if (!originalUserPrompt || this.queryCleanupObserver) {
        return;
      }

      console.log('[Perplexity MCP] 🔄 Starting real-time query cleanup monitoring');

      let lastCleanupActivity = Date.now();
      let hasFoundTargetElement = false;
      const inactivityTimeout = 60000; // 1 minute of no cleanup activity

      // Create MutationObserver to watch for new query elements
      this.queryCleanupObserver = new MutationObserver((mutations) => {
        let activityDetected = false;

        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const addedNode of mutation.addedNodes) {
              if (addedNode.nodeType === Node.ELEMENT_NODE) {
                // Check if this is a query element or contains query elements
                const cleanupResult = this.checkAndCleanupQueryElement(addedNode, originalUserPrompt);
                if (cleanupResult) {
                  activityDetected = true;
                  hasFoundTargetElement = true;
                }
              }
            }
          }
        }

        if (activityDetected) {
          lastCleanupActivity = Date.now();
        }
      });

      // Start observing the document for new elements
      this.queryCleanupObserver.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Store original prompt for reference
      this.lastUserPrompt = originalUserPrompt;

      // Periodic inactivity checker
      const inactivityChecker = setInterval(() => {
        const timeSinceLastActivity = Date.now() - lastCleanupActivity;

        if (timeSinceLastActivity > inactivityTimeout || hasFoundTargetElement) {
          this.queryCleanupObserver.disconnect();
          this.queryCleanupObserver = null;
          clearInterval(inactivityChecker);

          const reason = hasFoundTargetElement ? 'target found and cleaned' : `inactivity (${timeSinceLastActivity}ms since last activity)`;
          console.log(`[Perplexity MCP] ⏰ Auto-stopping query cleanup observer due to: ${reason}`);
        }
      }, 10000); // Check every 10 seconds

      // Fallback safety timeout
      setTimeout(() => {
        if (this.queryCleanupObserver) {
          this.queryCleanupObserver.disconnect();
          this.queryCleanupObserver = null;
          clearInterval(inactivityChecker);
          console.log('[Perplexity MCP] ⏰ Query cleanup observer reached maximum safety timeout (2 minutes)');
        }
      }, 120000); // 2 minutes maximum

      console.log('[Perplexity MCP] ✅ Real-time query cleanup monitoring started');
    }

    // Check if an element is or contains a query element and clean it up
    checkAndCleanupQueryElement(element, originalUserPrompt) {
      // Check for query display elements
      const querySelector = '.pt-md.md\\:pt-lg>div>div>div>div>div>div';

      let queryElements = [];
      let cleanupActivity = false;

      // Check if the element itself matches
      if (element.matches && element.matches(querySelector)) {
        queryElements.push(element);
      }

      // Check if the element contains matching elements
      if (element.querySelectorAll) {
        const foundElements = element.querySelectorAll(querySelector);
        queryElements.push(...Array.from(foundElements));
      }

      // Process each found query element
      for (const queryElement of queryElements) {
        const result = this.processQueryElementForCleanup(queryElement, originalUserPrompt);
        if (result) cleanupActivity = true;
      }

      // Also check for answer mode tabs elements
      const answerModeResult = this.checkAndCleanupAnswerModeTabs(element, originalUserPrompt);
      if (answerModeResult) cleanupActivity = true;

      return cleanupActivity;
    }

    // Check if an element is or contains answer mode tabs elements and clean them up
    checkAndCleanupAnswerModeTabs(element, originalUserPrompt) {
      const answerModeSelector = 'div[data-testid="answer-mode-tabs"]>div>div.hidden';

      let answerModeElements = [];
      let cleanupActivity = false;

      // Check if the element itself matches
      if (element.matches && element.matches(answerModeSelector)) {
        answerModeElements.push(element);
      }

      // Check if the element contains matching elements
      if (element.querySelectorAll) {
        const foundElements = element.querySelectorAll(answerModeSelector);
        answerModeElements.push(...Array.from(foundElements));
      }

      // Process each found answer mode element
      for (const answerModeElement of answerModeElements) {
        const result = this.processAnswerModeElementForCleanup(answerModeElement, originalUserPrompt);
        if (result) cleanupActivity = true;
      }

      return cleanupActivity;
    }

    // Process individual answer mode tabs element for cleanup
    processAnswerModeElementForCleanup(answerModeElement, originalUserPrompt) {
      if (!answerModeElement) {
        return false;
      }

      const currentText = answerModeElement.textContent || '';

      // Only clean up if this element contains enhancement markers AND has the full content
      // Note: This text doesn't have \n, it's all one text block with spaces
      if ((currentText.includes('--------------------------------') ||
        currentText.includes('MCP TOOLS ENHANCEMENT') ||
        currentText.includes('Available MCP Tools')) &&
        currentText.includes(originalUserPrompt)) {

        console.log('[Perplexity MCP] 🧹 Real-time cleanup: Found enhanced answer mode tabs element');
        console.log('[Perplexity MCP] 📝 Current text length:', currentText.length);
        console.log('[Perplexity MCP] 🔍 Contains original prompt:', currentText.includes(originalUserPrompt));

        // Wait a brief moment to ensure the content is fully loaded
        setTimeout(() => {
          // Double-check the content is still there and complete
          const finalText = answerModeElement.textContent || '';
          if (finalText.includes(originalUserPrompt) &&
            (finalText.includes('--------------------------------') ||
              finalText.includes('MCP TOOLS ENHANCEMENT') ||
              finalText.includes('Available MCP Tools'))) {

            console.log('[Perplexity MCP] ✅ Cleaning up enhanced answer mode tabs with original prompt');

            // Replace with just the original user prompt
            answerModeElement.textContent = originalUserPrompt;

            console.log('[Perplexity MCP] ✅ Real-time answer mode tabs cleanup successful');
          }
        }, 100); // Brief delay to ensure content is fully rendered

        return true; // Activity detected
      }

      return false; // No activity
    }

    // Process individual query element for cleanup
    processQueryElementForCleanup(queryElement, originalUserPrompt) {
      if (!queryElement.children[0]) {
        return false;
      }

      const contentElement = queryElement.children[0];
      const currentText = contentElement.textContent || '';

      // Only clean up if this element contains enhancement markers AND has the full content
      if ((currentText.includes('--------------------------------') ||
        currentText.includes('MCP TOOLS ENHANCEMENT') ||
        currentText.includes('Available MCP Tools')) &&
        currentText.includes(originalUserPrompt)) {

        console.log('[Perplexity MCP] 🧹 Real-time cleanup: Found enhanced query element');
        console.log('[Perplexity MCP] 📝 Current text length:', currentText.length);
        console.log('[Perplexity MCP] 🔍 Contains original prompt:', currentText.includes(originalUserPrompt));

        // Wait a brief moment to ensure the content is fully loaded
        setTimeout(() => {
          // Double-check the content is still there and complete
          const finalText = contentElement.textContent || '';
          if (finalText.includes(originalUserPrompt) &&
            (finalText.includes('--------------------------------') ||
              finalText.includes('MCP TOOLS ENHANCEMENT') ||
              finalText.includes('Available MCP Tools'))) {

            console.log('[Perplexity MCP] ✅ Cleaning up enhanced query with original prompt');

            // Replace with just the original user prompt
            contentElement.textContent = originalUserPrompt;

            // Set height to auto to prevent layout issues
            contentElement.style.setProperty('height', 'auto', 'important');

            console.log('[Perplexity MCP] ✅ Real-time cleanup successful');

            // Stop monitoring since we found and cleaned the target
            this.stopRealtimeQueryCleanup();
          }
        }, 100); // Brief delay to ensure content is fully rendered

        return true; // Activity detected
      }

      return false; // No activity
    }

    // Stop the real-time query cleanup observer
    stopRealtimeQueryCleanup() {
      if (this.queryCleanupObserver) {
        this.queryCleanupObserver.disconnect();
        this.queryCleanupObserver = null;
        console.log('[Perplexity MCP] 🛑 Stopped real-time query cleanup monitoring');
      }
    }

    // New method to cleanly remove tool call text from response
    cleanupToolCallFromResponse(element, toolCallText) {
      try {
        console.log('[Perplexity MCP] 🧹 Cleaning up tool call text from response');
        console.log('[Perplexity MCP] 🎯 Looking for exact tool call text:', toolCallText);

        // Find the specific content div: div.pb-lg > div > div > div > div > div > div.relative
        const contentDiv = element.querySelector('div > div > div > div > div > div.relative');

        if (!contentDiv) {
          console.log('[Perplexity MCP] ⚠️ Could not find content div with selector: div > div > div > div > div > div.relative');
          return;
        }

        // Get the text content of the content div
        const textContent = contentDiv.textContent || '';
        console.log('[Perplexity MCP] 📝 Content div text length:', textContent.length);

        // Find the exact tool call text
        const toolCallIndex = textContent.indexOf(toolCallText);

        if (toolCallIndex === -1) {
          console.log('[Perplexity MCP] ⚠️ Exact tool call text not found in content div');
          console.log('[Perplexity MCP] 🔍 Text sample:', textContent.substring(0, 200) + '...');
          return;
        }

        console.log('[Perplexity MCP] ✅ Found exact tool call text at index:', toolCallIndex);

        // Keep everything before the tool call, preserving formatting
        const beforeToolCall = textContent.substring(0, toolCallIndex);

        // Update the content div's text content
        contentDiv.textContent = beforeToolCall;

        console.log('[Perplexity MCP] ✅ Successfully cleaned up tool call text');
        console.log('[Perplexity MCP] 📏 New content length:', beforeToolCall.length);

      } catch (error) {
        console.error('[Perplexity MCP] Error cleaning up tool call text:', error);
      }
    }

    // Rewritten method to parse the FIRST tool call using Regex, with enhanced internal logging.
    parseAndExecuteFirstToolCall(element, text) {
      if (!element) {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] parseAndExecuteFirstToolCall: element is null. Returning false.');
        return false;
      }
      if (element.dataset.mcpToolCallHandled === 'true') {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] Tool call in element already handled, skipping parse. Returning true.');
        return true;
      }

      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] 🔍 Attempting to parse tool call from text (length ' + text.length + '):', text.substring(0, 300) + '...');
      }

      const xmlStartTag = '<mcp_tool';
      const xmlStartPos = text.indexOf(xmlStartTag);
      if (xmlStartPos === -1) {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] ❌ Step 1 Fail: No <mcp_tool start tag found. Returning false.');
        return false;
      }
      if (this.settings.debugLogging) console.log('[Perplexity MCP] ✅ Step 1 Pass: Found <mcp_tool start tag at pos ' + xmlStartPos);

      const xmlEndTag = '</mcp_tool>';
      const xmlEndTagPos = text.indexOf(xmlEndTag, xmlStartPos + xmlStartTag.length);
      if (xmlEndTagPos === -1) {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] ❌ Step 2 Fail: No </mcp_tool> end tag found after start tag. Returning false.');
        return false;
      }
      if (this.settings.debugLogging) console.log('[Perplexity MCP] ✅ Step 2 Pass: Found </mcp_tool> end tag at pos ' + xmlEndTagPos);

      const xmlBlock = text.substring(xmlStartPos, xmlEndTagPos + xmlEndTag.length);
      if (this.settings.debugLogging) console.log('[Perplexity MCP] 🔍 Extracted XML block for processing (length ' + xmlBlock.length + '):', xmlBlock.substring(0, 300) + "...");

      let serverId, toolName;
      const parameters = {};

      const mcpToolTagRegex = /<mcp_tool\s+server="([^"]+)"\s+tool="([^"]+)"\s*>/;
      const mcpToolMatch = xmlBlock.match(mcpToolTagRegex);

      if (!mcpToolMatch || mcpToolMatch.length < 3) {
        if (this.settings.debugLogging) {
          const openingTagAttempt = xmlBlock.substring(0, xmlBlock.indexOf('>') + 1);
          console.log('[Perplexity MCP] ❌ Step 3 Fail: Regex could not parse server/tool attributes from <mcp_tool> tag.',
            'Regex:', mcpToolTagRegex.source,
            'Input (opening tag):', openingTagAttempt,
            'Match result:', mcpToolMatch,
            '. Returning false.');
        }
        return false;
      }
      serverId = mcpToolMatch[1];
      toolName = mcpToolMatch[2];
      if (this.settings.debugLogging) console.log(`[Perplexity MCP] ✅ Step 3 Pass: Parsed server="${serverId}", tool="${toolName}"`);

      const mcpToolOpenTagActualEndPos = xmlBlock.indexOf('>');
      if (mcpToolOpenTagActualEndPos === -1) {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] ❌ Step 4 Fail: Could not find closing > for <mcp_tool ...> tag. This implies Step 3 was flawed. Returning false.');
        return false;
      }
      if (this.settings.debugLogging) console.log('[Perplexity MCP] ✅ Step 4 Pass: Found end of opening tag at pos ' + mcpToolOpenTagActualEndPos);

      const paramsBlockString = xmlBlock.substring(mcpToolOpenTagActualEndPos + 1, xmlBlock.lastIndexOf(xmlEndTag));
      if (this.settings.debugLogging) console.log('[Perplexity MCP] 🔍 Extracted paramsBlockString (length ' + paramsBlockString.length + '):', paramsBlockString.substring(0, 200) + "...");

      const paramRegex = /<([a-zA-Z0-9_:-]+)>([\s\S]*?)<\/\1\s*>/g;
      let paramMatch;
      let paramsFoundCount = 0;
      while ((paramMatch = paramRegex.exec(paramsBlockString)) !== null) {
        paramsFoundCount++;
        const paramNameStr = paramMatch[1];
        let paramValueStr = paramMatch[2];

        paramValueStr = paramValueStr.replace(/</g, '<')
          .replace(/>/g, '>')
          .replace(/&/g, '&')
          .replace(/"/g, '"')
          .replace(/'/g, "'");
        parameters[paramNameStr] = paramValueStr;
      }
      if (this.settings.debugLogging) console.log(`[Perplexity MCP] ✅ Step 5 Pass: Found ${paramsFoundCount} parameter(s) using regex: ${paramRegex.source}`);

      if (this.settings.debugLogging) {
        console.log('[Perplexity MCP] 🎯 Final parsed tool call details:', { serverId, toolName });
        Object.keys(parameters).forEach(key => {
          const value = parameters[key];
          if (value && typeof value === 'string' && value.length > 200) {
            console.log(`[Perplexity MCP] Param "${key}" (length ${value.length}, snippet):`, value.substring(0, 200) + "...");
          } else {
            console.log(`[Perplexity MCP] Param "${key}":`, value);
          }
        });
      }

      this.cleanupToolCallFromResponse(element, xmlBlock);

      const toolCall = {
        tool: toolName,
        server: serverId,
        parameters,
        originalText: xmlBlock,
        element: element,
        id: `${serverId}-${toolName}-${Date.now()}`
      };

      if (!this.settings.legacyMode) {
        this.handleMcpToolDetected(toolCall);
      } else {
        this.createInlineToolWidget(xmlBlock, element, toolCall);
      }

      element.dataset.mcpToolCallHandled = 'true';
      if (this.settings.debugLogging) console.log('[Perplexity MCP] ✅ parseAndExecuteFirstToolCall successful. Returning true.');
      return true;
    }

    attemptProcessElementContent(element, attemptNumber) {
      if (!element) {
        if (this.settings.debugLogging) console.log(`[Perplexity MCP] attemptProcessElementContent: Element is null on attempt ${attemptNumber}.`);
        return;
      }

      const textContent = element.textContent || '';
      if (this.settings.debugLogging) {
        console.log(`[Perplexity MCP] Attempt ${attemptNumber + 1}/${this.seamlessMode.MAX_PROCESSING_ATTEMPTS} to process element. Content snippet: "${textContent.substring(0, 100)}..."`);
      }

      const toolCallFoundAndInitiated = this.parseAndExecuteToolCall(element, textContent);

      if (toolCallFoundAndInitiated) {
        if (this.settings.debugLogging) console.log('[Perplexity MCP] Tool call found and initiated processing for element:', element);
        // Successfully processed, no more retries needed.
        // The mcpProcessingQueued flag remains true, indicating it has been through the queue.
      } else if (attemptNumber < this.seamlessMode.MAX_PROCESSING_ATTEMPTS - 1) {
        const nextDelay = this.seamlessMode.PROCESSING_RETRY_DELAYS[attemptNumber];
        if (this.settings.debugLogging) {
          console.log(`[Perplexity MCP] No tool call found on attempt ${attemptNumber + 1}. Retrying in ${nextDelay}ms for element:`, element);
        }
        setTimeout(() => this.attemptProcessElementContent(element, attemptNumber + 1), nextDelay);
      } else {
        if (this.settings.debugLogging) {
          console.warn('[Perplexity MCP] Max attempts reached. No tool call found in element after all retries:', element, `Final content snippet: "${textContent.substring(0, 100)}..."`);
        }
        // Mark as definitively failed to find after retries if needed, though mcpProcessingQueued = true already indicates it was handled.
      }
    }

    // Renamed from processResponseElement and refactored to focus on parsing and execution
    parseAndExecuteToolCall(element, text) {
      if (!element) {
        console.log('[Perplexity MCP] parseAndExecuteToolCall: element is null.');
        return false;
      }

      // If this element's tool call has already been handled, don't re-process.
      if (element.dataset.mcpToolCallHandled === 'true') {
        console.log('[Perplexity MCP] Tool call in element already handled, skipping parse:', element);
        return true; // Signify it was "found" (previously) and handled.
      }

      console.log('[Perplexity MCP] 🔍 parseAndExecuteToolCall called for element:', {
        element: element,
        textLength: text.length,
        textSample: text.substring(0, 200) + '...',
        hasToolCallString: text.includes('mcpExecuteTool'),
        elementTag: element.tagName,
        elementClass: element.className
      });

      // Check if text contains the tool call pattern first
      if (!this.hasToolCallPattern(text)) {
        console.log('[Perplexity MCP] ❌ No tool call pattern found in text, skipping parsing');
        return false;
      }

      console.log('[Perplexity MCP] ✅ Tool call pattern detected, proceeding with parsing');

      const toolCallPatterns = [
        // Pattern 1: Quoted parameters - more robust handling of escaped characters and Windows paths
        /mcpExecuteTool\s*\(\s*["']([^"']+)["']\s*,\s*["']([^"']+)["']\s*,?\s*(\{(?:[^{}]|\\\\.|"(?:[^"\\\\]|\\\\[\\\\"])*")*\})?\s*\)/g,
        // Pattern 2: Unquoted parameters
        /mcpExecuteTool\s*\(\s*([^,\s"']+)\s*,\s*([^,\s"']+)\s*,?\s*(\{(?:[^{}]|\\\\.|"(?:[^"\\\\]|\\\\[\\\\"])*")*\})?\s*\)/g,
        // Pattern 3: More flexible whitespace and parameter handling
        /mcpExecuteTool\s*\(\s*["']?([^"',\s]+)["']?\s*,\s*["']?([^"',\s]+)["']?\s*,?\s*(\{(?:[^{}]|\\\\.|"(?:[^"\\\\]|\\\\[\\\\"])*")*\})?\s*\)/g
      ];

      let foundAndInitiated = false;

      for (const pattern of toolCallPatterns) {
        if (foundAndInitiated) break;

        const matches = text.matchAll(pattern);
        for (const match of matches) {
          if (this.settings.debugLogging) {
            console.log('[Perplexity MCP] 🎯 Found tool call match:', match);
          }

          // CLEAN APPROACH: Remove tool call text and everything after it
          this.cleanupToolCallFromResponse(element, match[0]);

          this.handleDetectedToolCall(match, element, pattern);
          element.dataset.mcpToolCallHandled = 'true'; // Mark as handled
          foundAndInitiated = true;
          break;
        }
      }

      if (!foundAndInitiated) {
        try {
          const jsonPattern = /\{[^}]*"tool"[^}]*\}/g;
          const jsonMatches = text.matchAll(jsonPattern);
          for (const match of jsonMatches) {
            try {
              const toolCall = JSON.parse(match[0]);
              if (toolCall.tool && toolCall.parameters) {
                // CLEAN APPROACH: Remove tool call text and everything after it
                this.cleanupToolCallFromResponse(element, match[0]);

                this.executeDetectedToolCall(toolCall, element);
                element.dataset.mcpToolCallHandled = 'true'; // Mark as handled
                foundAndInitiated = true;
                break;
              }
            } catch (e) { /* Not valid JSON */ }
          }
        } catch (e) { /* Error with regex or matchAll */ }
      }
      return foundAndInitiated;
    }

    handleDetectedToolCall(match, element, pattern) {
      if (this.settings.debugLogging) console.log('[Perplexity MCP] Detected potential tool call:', match);

      // Skip if this looks like system prompt content
      const fullText = element.textContent || '';
      if (fullText.includes('Available MCP Tools') ||
        fullText.includes('CRITICAL MCP TOOL USAGE RULES') ||
        fullText.includes('Example Workflow') ||
        fullText.includes('serverId') && fullText.includes('toolName')) {
        if (this.settings.debugLogging) {
          console.log('[Perplexity MCP] Skipping tool call - appears to be system prompt content');
        }
        return;
      }

      // Try to extract tool information and execute
      let toolName, serverId, parameters = {};

      // Check if this is an mcpExecuteTool function call (first pattern)
      if (pattern.source.includes('mcpExecuteTool')) {
        serverId = match[1]; // First parameter is server ID
        toolName = match[2]; // Second parameter is tool name
        if (match[3]) {
          try {
            // More robust JSON parameter parsing - handle Windows paths properly
            let paramStr = match[3];

            console.log('[Perplexity MCP] 🔧 Original param string:', paramStr);

            // Handle Windows paths properly - be more careful with backslash handling
            // Don't modify paths that are already properly escaped
            if (!paramStr.includes('\\\\')) {
              // Only if we don't already have double backslashes, escape single ones
              paramStr = paramStr.replace(/\\/g, '\\\\');
              console.log('[Perplexity MCP] 🔧 After Windows path escaping:', paramStr);
            } else {
              console.log('[Perplexity MCP] 🔧 Already has escaped backslashes, keeping as-is');
            }

            // Replace unquoted keys with quoted keys (but be careful not to match inside values)
            // Only match word characters followed by colon that are at the start or after { or ,
            paramStr = paramStr.replace(/([{,]\s*)(\w+):/g, '$1"$2":');
            console.log('[Perplexity MCP] 🔧 After key quoting:', paramStr);

            // Replace single quotes with double quotes, but be careful around escaped characters
            paramStr = paramStr.replace(/'/g, '"');
            console.log('[Perplexity MCP] 🔧 After quote replacement:', paramStr);

            // Now try to parse
            console.log('[Perplexity MCP] 🔧 Attempting to parse:', paramStr);
            parameters = JSON.parse(paramStr);
            console.log('[Perplexity MCP] ✅ Successfully parsed parameters:', parameters);
          } catch (e) {
            console.warn('[Perplexity MCP] Failed to parse parameters:', match[3]);
            console.log('[Perplexity MCP] Original param string:', match[3]);
            console.log('[Perplexity MCP] Attempted to parse cleaned version:', paramStr);
            console.log('[Perplexity MCP] Parse error:', e.message);
            parameters = {};
          }
        }

        console.log('[Perplexity MCP] Parsed mcpExecuteTool call:', { serverId, toolName, parameters });

        const toolCall = {
          tool: toolName,
          server: serverId,
          parameters,
          originalText: match[0],
          element: element,
          id: `${serverId}-${toolName}-${Date.now()}`
        };

        // Check if we're in seamless mode
        if (!this.settings.legacyMode) {
          this.handleMcpToolDetected(toolCall);
        } else {
          // Legacy behavior: create inline widget
          this.createInlineToolWidget(match[0], element, toolCall);
        }
      }
      // Handle other patterns
      else if (match[1] && match[2]) {
        // Extract from pattern match
        toolName = match[1];
        try {
          parameters = JSON.parse(match[2]);
        } catch (e) {
          parameters = { query: match[2] };
        }

        // Find appropriate server for this tool
        serverId = this.findServerForTool(toolName);

        if (serverId) {
          const toolCall = {
            tool: toolName,
            server: serverId,
            parameters,
            originalText: match[0],
            element: element,
            id: `${serverId}-${toolName}-${Date.now()}`
          };

          if (!this.settings.legacyMode) {
            this.handleMcpToolDetected(toolCall);
          } else {
            this.createInlineToolWidget(match[0], element, toolCall);
          }
        } else {
          console.warn('[Perplexity MCP] No server found for tool:', toolName);
        }
      }
    }

    handleMcpToolDetected(toolCall) {
      if (this.settings.legacyMode) {
        // Use legacy behavior - just show widget
        return this.createInlineToolWidget(toolCall.originalText, toolCall.element, toolCall);
      }

      // Seamless mode behavior
      console.log('[Perplexity MCP] 🎯 MCP tool detected in seamless mode:', {
        tool: toolCall.tool,
        server: toolCall.server,
        element: toolCall.element,
        currentPbLgCount: document.querySelectorAll('.pb-lg').length
      });

      // Step 1: Find and modify the last .pb-lg element ONLY when tool call is confirmed
      console.log('[Perplexity MCP] 🔧 Calling modifyLastPbElementForToolCall for:', toolCall.tool);
      this.modifyLastPbElementForToolCall(); // Fire and forget - don't await to avoid blocking

      // Step 2: Save current response element count
      this.seamlessMode.responseElementCount = document.querySelectorAll('div.-inset-md.absolute').length;

      // Step 3: Create the visual widget for user feedback
      console.log('[Perplexity MCP] 📱 Creating inline widget for:', toolCall.tool);
      const widget = this.createInlineToolWidget(toolCall.originalText, toolCall.element, toolCall);

      // Step 4: Execute tool and handle seamless workflow after completion
      console.log('[Perplexity MCP] ⚡ Starting tool execution for:', toolCall.tool);
      this.executeSeamlessToolCallWithWidget(toolCall, widget);

      // Step 5: Queue the response deletion ONLY for tool call responses
      this.seamlessMode.pendingDeletions.push({
        toolCall: toolCall,
        timestamp: Date.now(),
        elementToDelete: toolCall.element // Store reference to the specific element
      });

      console.log('[Perplexity MCP] 📋 Queued deletion for tool call response. Pending deletions:', this.seamlessMode.pendingDeletions.length);

      // Save state
      this.saveThreadState();
    }

    async modifyLastPbElementForToolCall() {
      // Skip modification during restoration
      if (this.stateManager.isRestoring) {
        console.log('[Perplexity MCP] Skipping .pb-lg modification during restoration');
        return;
      }

      const pbElements = document.querySelectorAll('.pb-lg');
      const lastPbElement = pbElements[pbElements.length - 1];

      if (lastPbElement) {
        console.log('[Perplexity MCP] 🔧 Starting DOM modification for tool call. Total .pb-lg elements:', pbElements.length);

        // Capture original state before modification
        this.captureElementState(lastPbElement, 'pb-element-modified', {
          originalBorderBottomWidth: lastPbElement.style.borderBottomWidth,
          originalPaddingBottom: lastPbElement.style.paddingBottom
        });

        // Set border-bottom-width to 0 and padding-bottom to 0
        lastPbElement.style.setProperty('border-bottom-width', '0', 'important');
        lastPbElement.style.setProperty('padding-bottom', '0', 'important');

        // Wait for the target element to appear with retry mechanism
        await this.waitForAndRemoveFlexElement(lastPbElement);

        console.log('[Perplexity MCP] ✅ Modified last .pb-lg element for tool call');
      } else {
        console.log('[Perplexity MCP] ❌ No .pb-lg elements found for modification');
      }
    }

    async waitForAndRemoveFlexElement(pbElement, maxWaitMs = 10000) {
      const targetSelector = 'div > div > div > div > div > div.flex.items-center.justify-between';

      console.log('[Perplexity MCP] 🔍 Starting real-time monitoring for flex element...');

      return new Promise((resolve) => {
        // Check if element already exists
        const existingElement = pbElement.querySelector(targetSelector);
        if (existingElement) {
          existingElement.remove();
          console.log('[Perplexity MCP] ✅ Flex element found immediately and removed');
          resolve(true);
          return;
        }

        // Set up real-time MutationObserver to watch for the element
        const observer = new MutationObserver((mutations) => {
          let activityDetected = false;

          for (const mutation of mutations) {
            if (mutation.type === 'childList') {
              activityDetected = true;
              lastActivity = Date.now();

              for (const addedNode of mutation.addedNodes) {
                if (addedNode.nodeType === Node.ELEMENT_NODE) {
                  // Check if the added node matches or contains our target
                  let targetElement = null;

                  if (addedNode.matches && addedNode.matches(targetSelector)) {
                    targetElement = addedNode;
                  } else if (addedNode.querySelector) {
                    targetElement = addedNode.querySelector(targetSelector);
                  }

                  if (targetElement) {
                    targetElement.remove();
                    console.log('[Perplexity MCP] ✅ Real-time: Found and removed flex element via MutationObserver');
                    elementFound = true;
                    observer.disconnect();
                    clearInterval(inactivityChecker);
                    resolve(true);
                    return;
                  }
                }
              }
            }
          }
        });

        // Start observing the pbElement for changes
        observer.observe(pbElement, {
          childList: true,
          subtree: true
        });

        // Track activity for inactivity-based cleanup
        let lastActivity = Date.now();
        let elementFound = false;

        // Periodic inactivity checker
        const inactivityChecker = setInterval(() => {
          const timeSinceLastActivity = Date.now() - lastActivity;

          if (timeSinceLastActivity > 30000 || elementFound) { // 30 seconds of inactivity
            observer.disconnect();
            clearInterval(inactivityChecker);

            const reason = elementFound ? 'element found and removed' : `inactivity (${timeSinceLastActivity}ms)`;
            console.log(`[Perplexity MCP] ⚠️ Real-time flex element monitoring stopped due to: ${reason}`);
            resolve(elementFound);
          }
        }, 5000); // Check every 5 seconds

        // Auto-timeout after maxWaitMs as fallback
        setTimeout(() => {
          observer.disconnect();
          clearInterval(inactivityChecker);
          console.log('[Perplexity MCP] ⚠️ Real-time flex element monitoring reached maximum timeout after', maxWaitMs, 'ms');
          console.log('[Perplexity MCP] Available elements in .pb-lg:', pbElement.innerHTML.substring(0, 300) + '...');
          resolve(false);
        }, maxWaitMs);

        console.log('[Perplexity MCP] 🔄 Real-time flex element monitoring started (timeout:', maxWaitMs, 'ms)');
      });
    }

    async executeSeamlessToolCallWithWidget(toolCall, widget) {
      console.log('[Perplexity MCP] Executing tool call with seamless workflow:', toolCall);
      toolCall.resultSentState = 'pending_execution'; // 'pending_execution', 'sending_to_ai', 'sent_to_ai', 'failed_to_send'

      this.seamlessMode.activeToolCalls.set(toolCall.id || Date.now(), {
        toolCall: toolCall,
        status: 'pending', // For widget
        startTime: Date.now()
      });

      try {
        const result = await this.executeInlineToolCall(toolCall, widget); // This updates widget
        console.log('[Perplexity MCP] Tool execution completed successfully.');

        if (toolCall.resultSentState === 'sent_to_ai' || toolCall.resultSentState === 'failed_to_send') {
          console.log('[Perplexity MCP] executeSeamlessToolCallWithWidget: Result/Error already sent or failed definitively. Skipping AI submission. State:', toolCall.resultSentState);
          return;
        }

        toolCall.resultSentState = 'sending_to_ai';
        console.log('[Perplexity MCP] Attempting to send tool result to AI.');
        await this.sendToolResultToAI(toolCall, null, result);
        // sendToolResultToAI will update the state to 'sent_to_ai' or 'failed_to_send'
        // No need to set toolCall.resultSentState = 'sent_to_ai'; here as sendToolResultToAI handles it.
        // console.log('[Perplexity MCP] Tool result successfully sent to AI.'); // Log moved to sendToolResultToAI

      } catch (error) {
        console.error('[Perplexity MCP] Seamless tool execution failed or error during AI submission:', error);

        if (toolCall.resultSentState === 'sent_to_ai' || toolCall.resultSentState === 'failed_to_send') {
          console.log('[Perplexity MCP] executeSeamlessToolCallWithWidget (catch): Error already processed or result sent. Skipping duplicate error handling. State:', toolCall.resultSentState);
          return;
        }

        // If executeInlineToolCall itself failed, the widget shows the error.
        // We still try to inform the AI about the tool error, unless it's already being handled or finished.
        if (toolCall.resultSentState !== 'sending_to_ai') {
          console.log('[Perplexity MCP] Attempting to send tool error to AI after execution failure.');
          // sendToolResultToAI will set the state to 'sending_to_ai', then 'sent_to_ai' or 'failed_to_send'
          await this.sendToolResultToAI(toolCall, error, null);
        } else {
          // If it was already 'sending_to_ai' and an error occurred (e.g. in sendToolResultToAI itself)
          // sendToolResultToAI would have set it to 'failed_to_send'.
          // If the error is from executeInlineToolCall and sendToolResultToAI was ongoing, this path might be complex.
          // For now, we assume sendToolResultToAI handles its own failure state.
          console.warn('[Perplexity MCP] Error occurred while already in "sending_to_ai" state. Current state:', toolCall.resultSentState);
        }
      }
    }

    async sendToolResultToAI(toolCall, error = null, preExecutedResult = null) {
      if (toolCall.resultSentState === 'sent_to_ai' || toolCall.resultSentState === 'failed_to_send') {
        console.log(`[Perplexity MCP] sendToolResultToAI: Submission for tool call ${toolCall.id} already completed or failed. State: ${toolCall.resultSentState}. Aborting.`);
        return;
      }

      try {
        toolCall.resultSentState = 'sending_to_ai'; // Mark as actively trying to send
        console.log('[Perplexity MCP] Preparing to send tool result/error to AI model. Tool Call ID:', toolCall.id);

        let hiddenTextarea = this.seamlessMode.hiddenTextarea;

        // More robust textarea acquisition with safety checks
        if (!hiddenTextarea || !document.body || !document.body.contains(hiddenTextarea)) {
          console.warn('[Perplexity MCP] Hidden textarea reference lost. Attempting to re-acquire from DOM.');

          // Safety check for document and body
          if (!document || !document.body) {
            console.error('[Perplexity MCP] CRITICAL: Document or body not available. DOM may not be ready.');
            toolCall.resultSentState = 'failed_to_send';
            throw new Error('Document not ready for textarea operations.');
          }

          try {
            hiddenTextarea = document.querySelector('textarea#ask-input'); // The original Perplexity textarea
          } catch (queryError) {
            console.error('[Perplexity MCP] Error querying for textarea:', queryError);
            hiddenTextarea = null;
          }

          if (!hiddenTextarea) {
            console.error('[Perplexity MCP] CRITICAL: Cannot find Perplexity input textarea (textarea#ask-input). Aborting send.');
            toolCall.resultSentState = 'failed_to_send';
            throw new Error('Cannot find Perplexity input textarea for AI submission.');
          }

          this.seamlessMode.hiddenTextarea = hiddenTextarea; // Update reference

          // If seamless mode is active, the dual textarea setup might need verification/re-init
          if (!this.settings.legacyMode && this.seamlessMode.userTextarea) {
            try {
              if (hiddenTextarea.style.opacity !== '0' || !document.body.contains(this.seamlessMode.userTextarea)) {
                console.warn('[Perplexity MCP] Dual textarea state inconsistent after re-acquiring hidden textarea. Re-initializing dual system.');
                this.setupDualTextareaSystem(); // This will re-assign this.seamlessMode.hiddenTextarea
                hiddenTextarea = this.seamlessMode.hiddenTextarea;
                if (!hiddenTextarea) {
                  console.error('[Perplexity MCP] CRITICAL: Re-initialization of dual textarea failed. Aborting send.');
                  toolCall.resultSentState = 'failed_to_send';
                  throw new Error('Re-initialization of dual textarea failed during AI submission.');
                }
                await new Promise(resolve => setTimeout(resolve, 50)); // Brief pause for DOM
              }
            } catch (dualSystemError) {
              console.error('[Perplexity MCP] Error in dual textarea system:', dualSystemError);
              // Continue with single textarea if dual system fails
            }
          }
        }

        console.log('[Perplexity MCP] Textarea system ready, preparing tool result payload.');

        let resultText;
        if (error) {
          resultText = `Tool execution failed: ${error.message || String(error)}`;
        } else if (preExecutedResult) {
          resultText = this.formatToolResult(preExecutedResult);
        } else {
          console.warn('[Perplexity MCP] sendToolResultToAI called without preExecutedResult or error. This should not happen.');
          toolCall.resultSentState = 'failed_to_send';
          throw new Error('sendToolResultToAI called without result or error.');
        }

        const followUpPrompt = `[MCP Tool Result from ${toolCall.server}/${toolCall.tool}]\n\n${resultText}`;
        console.log('[Perplexity MCP] Setting tool result in hidden textarea:', followUpPrompt);

        // Use background text sending method
        this.sendTextInBackground(hiddenTextarea, followUpPrompt);

        // Allow React more time to process the input event and update state
        console.log('[Perplexity MCP] Waiting for React to process input changes...');
        await new Promise(resolve => setTimeout(resolve, 200));

        // Update .pb-lg count before submitting the tool result
        if (!this.settings.legacyMode) {
          this.seamlessMode.lastPbLgCount = document.querySelectorAll('.pb-lg').length;
          console.log('[Perplexity MCP] Seamless: Before tool result submission, .pb-lg count:', this.seamlessMode.lastPbLgCount);
        }

        console.log('[Perplexity MCP] Submitting tool result via hidden textarea.');
        if (!this.isSubmittingToolResult) {
          this.isSubmittingToolResult = true;
          this.submitTextInBackground(hiddenTextarea);
          toolCall.resultSentState = 'sent_to_ai';
          console.log('[Perplexity MCP] Tool result submitted with lock.');
          // release lock after 1s
          setTimeout(() => { this.isSubmittingToolResult = false; }, 1000);
        } else {
          console.log('[Perplexity MCP] sendToolResultToAI: lock active, skipping submission.');
        }
        console.log('[Perplexity MCP] Tool result/error successfully submitted to AI. Tool Call ID:', toolCall.id);

      } catch (submissionError) {
        console.error('[Perplexity MCP] CRITICAL ERROR during sendToolResultToAI:', submissionError);
        toolCall.resultSentState = 'failed_to_send';
        // Re-throw so executeSeamlessToolCallWithWidget's catch can also see it if needed,
        // though the state is now definitively 'failed_to_send'.
        throw submissionError;
      }
    }

    handleSeamlessSubmission(userPrompt) {
      console.log('[Perplexity MCP] Handling seamless submission for:', userPrompt);

      // Skip if tool result is in flight
      if (this.isSubmittingToolResult) {
        console.log('[Perplexity MCP] Submission lock active, skipping seamless submission.');
        return;
      }

      // Do not re-submit MCP tool results as new prompts
      if (userPrompt.startsWith('[MCP Tool Result from')) {
        console.log('[Perplexity MCP] Detected tool result in overlay, skipping submission.');
        return;
      }

      const originalTextarea = this.seamlessMode.hiddenTextarea; // Original (hidden) textarea
      const overlayTextarea = this.seamlessMode.userTextarea;   // Overlay (visible) textarea

      if (!originalTextarea || !overlayTextarea) {
        console.error('[Perplexity MCP] Overlay textarea system not properly initialized');
        return;
      }

      // STEP 1: Query current .pb-lg count BEFORE submission (as requested)
      const currentPbLgCount = document.querySelectorAll('.pb-lg').length;
      this.seamlessMode.lastPbLgCount = currentPbLgCount;
      console.log('[Perplexity MCP] 📊 BEFORE SUBMISSION: Current .pb-lg count:', currentPbLgCount);

      // Check if we should enhance the prompt
      if (userPrompt.trim() && this.shouldEnhancePrompt(userPrompt)) {
        console.log('[Perplexity MCP] ✅ Enhancing prompt in seamless mode');
        const systemPrompt = this.generateMcpSystemPrompt();
        if (systemPrompt) {
          // NEW FORMAT: User query first, then enhancement
          const enhancedPrompt = `${userPrompt}${systemPrompt}`;

          // Use background text sending method for enhanced prompt
          console.log('[Perplexity MCP] 🎯 Setting enhanced prompt in original textarea');
          this.sendTextInBackground(originalTextarea, enhancedPrompt);

          // Store original user prompt for query cleanup
          this.lastUserPrompt = userPrompt;

          // Start real-time cleanup monitoring
          this.startRealtimeQueryCleanup(userPrompt);

          // Wait for React to process before submitting
          setTimeout(() => {
            this.submitTextInBackground(originalTextarea);
          }, 200);
          return;
        }
      }

      // No enhancement needed - submit user prompt as-is
      console.log('[Perplexity MCP] No enhancement needed, submitting user prompt as-is');
      this.submitTextInBackground(this.seamlessMode.hiddenTextarea);
    }

    submitViaOriginalTextarea() {
      const originalTextarea = this.seamlessMode.hiddenTextarea;

      console.log('[Perplexity MCP] 🚀 Starting submitViaOriginalTextarea using background method...');

      // Focus the textarea first
      originalTextarea.focus();

      // Use background text sending method to ensure React state is synchronized
      console.log('[Perplexity MCP] Re-dispatching input event for React synchronization...');
      this.sendTextInBackground(originalTextarea, originalTextarea.value);

      // Wait a moment for React to process, then use background submission
      setTimeout(() => {
        console.log('[Perplexity MCP] Using background submission method...');

        // Use the background submission method instead of keyboard events
        const success = this.submitTextInBackground(originalTextarea);

        if (success) {
          console.log('[Perplexity MCP] ✅ Background submission completed successfully');
        } else {
          console.warn('[Perplexity MCP] ⚠️ Background submission failed');
        }

      }, 200); // Wait 200ms for React to process the input event
    }

    submitNormalPrompt(userPrompt) {
      // Submit the user's original prompt without enhancement
      const userTextarea = this.seamlessMode.userTextarea || this.findActiveInput();
      if (userTextarea) {
        this.sendTextInBackground(userTextarea, userPrompt);

        setTimeout(() => {
          // Use background submission method
          this.submitTextInBackground(userTextarea);
        }, 10);
      }
    }

    async submitHiddenTextarea(textareaInstance) {
      if (!textareaInstance || !document.body.contains(textareaInstance)) {
        console.error('[Perplexity MCP] Invalid textarea instance provided for submission.');
        throw new Error('Invalid textarea for submission.');
      }

      console.log('[Perplexity MCP] Preparing to submit via hidden textarea using background method...');

      // Focus the textarea first
      textareaInstance.focus();
      console.log('[Perplexity MCP] Focused textarea, waiting for focus to settle...');

      // Wait for focus to settle
      await new Promise(resolve => setTimeout(resolve, 100));

      // Use background text sending method to ensure React state is synchronized
      console.log('[Perplexity MCP] Re-dispatching input event to ensure React state is synchronized...');
      this.sendTextInBackground(textareaInstance, textareaInstance.value);

      // Wait for React to process the input
      await new Promise(resolve => setTimeout(resolve, 200));

      // Use the new background submission method instead of keyboard events
      console.log('[Perplexity MCP] Submitting using background method...');
      const success = this.submitTextInBackground(textareaInstance);

      if (success) {
        console.log('[Perplexity MCP] ✅ Background submission completed successfully');
      } else {
        console.warn('[Perplexity MCP] ⚠️ Background submission failed');
        throw new Error('Background submission failed');
      }

      // Wait a moment for the submission to process
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    findServerForTool(toolName) {
      // Find which server has this tool
      for (const server of this.mcpServers) {
        if (server.tools && server.tools.some(tool => tool.name === toolName)) {
          return server.id;
        }
      }
      return null;
    }

    createInlineToolWidget(originalText, responseElement, toolCall) {
      // Check if we're in restoration mode - if so, don't create new widgets
      if (this.stateManager.isRestoring) {
        console.log('[Perplexity MCP] Skipping widget creation during restoration');
        return null;
      }

      // Safety checks
      if (!responseElement || !originalText || !toolCall) {
        console.error('[Perplexity MCP] createInlineToolWidget: Invalid parameters', {
          responseElement: !!responseElement,
          originalText: !!originalText,
          toolCall: !!toolCall
        });
        return this.createAnimatedToolWidget(toolCall); // Return widget anyway for seamless mode
      }

      // Check if the element is still in the DOM
      if (!document.body.contains(responseElement)) {
        console.warn('[Perplexity MCP] createInlineToolWidget: Element not in DOM, creating standalone widget');
        return this.createAnimatedToolWidget(toolCall);
      }

      try {
        // Find the exact text in the element and replace it with a widget
        const walker = document.createTreeWalker(
          responseElement,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        let textNode;
        while (textNode = walker.nextNode()) {
          if (textNode.textContent && textNode.textContent.includes(originalText)) {
            // Found the text node containing the tool call
            const parentElement = textNode.parentElement;
            if (!parentElement) {
              console.warn('[Perplexity MCP] createInlineToolWidget: Text node has no parent element');
              continue;
            }

            const fullText = textNode.textContent;
            const beforeText = fullText.substring(0, fullText.indexOf(originalText));
            const afterText = fullText.substring(fullText.indexOf(originalText) + originalText.length);

            // Create the inline widget as a block element
            const widget = this.createAnimatedToolWidget(toolCall);

            // Handle text before and after the tool call
            if (beforeText.trim() || afterText.trim()) {
              // Create text nodes for before and after content
              if (beforeText.trim()) {
                const beforeNode = document.createTextNode(beforeText);
                parentElement.insertBefore(beforeNode, textNode);
              }

              // Insert the widget directly (no container div)
              parentElement.insertBefore(widget, textNode);

              if (afterText.trim()) {
                const afterNode = document.createTextNode(afterText);
                parentElement.insertBefore(afterNode, textNode);
              }

              // Remove the original text node
              parentElement.removeChild(textNode);
            } else {
              // Simple replacement - just replace the text node with the widget
              try {
                parentElement.insertBefore(widget, textNode);
                parentElement.removeChild(textNode);
                console.log('[Perplexity MCP] Successfully replaced tool call text with widget');
              } catch (domError) {
                console.error('[Perplexity MCP] DOM manipulation error:', domError);
                // Fallback: just append the widget to the parent
                parentElement.appendChild(widget);
              }
            }

            // Capture widget state for persistence
            this.captureWidgetState(widget, toolCall, responseElement);

            // Only execute tool directly in legacy mode
            if (this.settings.legacyMode) {
              this.executeInlineToolCall(toolCall, widget);
            }

            // Return the widget for seamless mode
            return widget;
          }
        }

        // If no text node found with exact match, try a more flexible approach
        console.warn('[Perplexity MCP] Exact text match not found, trying flexible search for:', originalText.substring(0, 50));

        // Try to find any tool call pattern in the element
        const elementText = responseElement.textContent || '';
        if (elementText.includes('mcpExecuteTool')) {
          // Create widget and append directly to element
          const widget = this.createAnimatedToolWidget(toolCall);

          // Try to append the widget directly to the element
          try {
            responseElement.appendChild(widget);
            console.log('[Perplexity MCP] Fallback: Appended widget directly to response element');

            if (this.settings.legacyMode) {
              this.executeInlineToolCall(toolCall, widget);
            }

            return widget;
          } catch (appendError) {
            console.error('[Perplexity MCP] Failed to append widget:', appendError);
          }
        }

      } catch (error) {
        console.error('[Perplexity MCP] Error in createInlineToolWidget:', error);
      }

      // Final fallback: create standalone widget
      console.warn('[Perplexity MCP] Creating standalone widget as fallback');
      const standaloneWidget = this.createAnimatedToolWidget(toolCall);

      // Try to add it to the response element if possible
      try {
        if (responseElement && responseElement.appendChild) {
          responseElement.appendChild(standaloneWidget);
        }
      } catch (finalError) {
        console.error('[Perplexity MCP] Final fallback failed:', finalError);
      }

      return standaloneWidget;
    }

    createAnimatedToolWidget(toolCall) {
      const widget = document.createElement('div');
      widget.className = 'mcp-inline-tool-widget';
      widget.style.cssText = `
        display: block;
        width: 100%;
        margin: 15px 0 0 0;
        padding: 20px;
        border-radius: 12px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        position: relative;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
      `;

      // Add modern CSS animations and styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes mcpPulse {
          0%, 100% {
            opacity: 0.95;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.01);
          }
        }
        @keyframes mcpSlideIn {
          0% {
            opacity: 0;
            transform: translateY(-15px) scale(0.98);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        @keyframes mcpShake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
          20%, 40%, 60%, 80% { transform: translateX(3px); }
        }
        @keyframes mcpSpinDots {
          0%, 80%, 100% {
            transform: scale(0) rotate(0deg);
            opacity: 0.3;
          }
          40% {
            transform: scale(1.2) rotate(180deg);
            opacity: 1;
          }
        }
        .mcp-loading-dots {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          margin-right: 12px;
        }
        .mcp-loading-dots div {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: currentColor;
          animation: mcpSpinDots 1.4s linear infinite;
        }
        .mcp-loading-dots div:nth-child(1) { animation-delay: -0.32s; }
        .mcp-loading-dots div:nth-child(2) { animation-delay: -0.16s; }
        .mcp-loading-dots div:nth-child(3) { animation-delay: 0s; }
        .mcp-widget-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          font-weight: 600;
        }
        .mcp-widget-title {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .mcp-widget-icon {
          font-size: 18px;
        }
        .mcp-close-btn {
          background: rgba(0,0,0,0.1);
          color: currentColor;
          border: none;
          border-radius: 6px;
          padding: 4px 8px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s ease;
          opacity: 0.7;
        }
        .mcp-close-btn:hover {
          opacity: 1;
          background: rgba(0,0,0,0.2);
          transform: scale(1.1);
        }
        .mcp-status-section {
          background: rgba(255,255,255,0.1);
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 12px;
          backdrop-filter: blur(10px);
        }
        .mcp-status-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
        }
        .mcp-status-row:last-child {
          margin-bottom: 0;
        }
        .mcp-details {
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
        }
        .mcp-details summary {
          font-weight: 600;
          padding: 8px 0;
          list-style: none;
          position: relative;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .mcp-details summary::-webkit-details-marker {
          display: none;
        }
        .mcp-details summary::after {
          content: ' ▼';
          position: static;
          transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          font-size: 12px;
          margin-left: 6px;
          display: inline-block;
        }
        .mcp-details summary:hover::after {
          transform: scale(1.2);
        }
        .mcp-details[open] summary::after {
          transform: rotate(180deg);
        }
        .mcp-details[open] summary:hover::after {
          transform: rotate(180deg) scale(1.2);
        }
        .mcp-result-content {
          margin-top: 12px;
          padding: 12px;
          background: rgba(255,255,255,0.1);
          border-radius: 8px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          max-height: 300px;
          overflow-y: auto;
          white-space: pre-wrap;
          backdrop-filter: blur(5px);
          animation: mcpExpandContent 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
        @keyframes mcpExpandContent {
          0% {
            opacity: 0;
            transform: translateY(-10px);
            max-height: 0;
          }
          100% {
            opacity: 1;
            transform: translateY(0);
            max-height: 300px;
          }
        }
      `;
      if (!document.querySelector('style[data-mcp-animations]')) {
        style.setAttribute('data-mcp-animations', 'true');
        document.head.appendChild(style);
      }

      // Start with loading state
      this.setWidgetState(widget, 'loading', toolCall);

      return widget;
    }

    setWidgetState(widget, state, toolCall, data = null) {
      const startTime = toolCall.startTime || new Date();
      const currentTime = new Date();
      const duration = ((currentTime - startTime) / 1000).toFixed(1);

      const states = {
        loading: {
          background: 'linear-gradient(135deg, #20b2aa 0%, #1a9a92 100%)',
          color: 'white',
          animation: 'mcpPulse 3s ease-in-out infinite',
          content: `
            <div class="mcp-widget-header">
              <div class="mcp-widget-title">
                <div class="mcp-loading-dots">
                  <div></div><div></div><div></div>
                </div>
                <span>Executing MCP Tool: ${toolCall.server}/${toolCall.tool}</span>
              </div>
              <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
              <div class="mcp-status-row">
                <strong>Status:</strong>
                <span>⏳ Executing...</span>
              </div>
              <div class="mcp-status-row">
                <strong>Started:</strong>
                <span>${startTime.toLocaleTimeString()}</span>
              </div>
              <div class="mcp-status-row">
                <strong>Duration:</strong>
                <span class="mcp-stopwatch" data-start-time="${startTime.getTime()}">0.0s</span>
              </div>
            </div>
            <details class="mcp-details">
              <summary>Raw Tool Call</summary>
              <div class="mcp-result-content">${toolCall.originalText || `mcpExecuteTool("${toolCall.server}", "${toolCall.tool}", ${JSON.stringify(toolCall.parameters || {})})`}</div>
            </details>
            <details class="mcp-details">
              <summary>Request Details</summary>
              <div class="mcp-result-content">{
  "serverId": "${toolCall.server}",
  "toolName": "${toolCall.tool}",
  "parameters": ${JSON.stringify(toolCall.parameters || {}, null, 2)}
}</div>
            </details>
          `
        },
        success: {
          background: 'linear-gradient(180deg, #28a745 0%, #20b2aa 100%)',
          color: 'white',
          animation: 'mcpSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
          content: `
            <div class="mcp-widget-header">
              <div class="mcp-widget-title">
                <span class="mcp-widget-icon">✅</span>
                <span>MCP Tool Result: ${toolCall.server}/${toolCall.tool}</span>
              </div>
              <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
              <div class="mcp-status-row">
                <strong>Status:</strong>
                <span>✅ Success</span>
              </div>
              <div class="mcp-status-row">
                <strong>Executed:</strong>
                <span>${currentTime.toLocaleTimeString()}</span>
              </div>
              <div class="mcp-status-row">
                <strong>Duration:</strong>
                <span>${duration}s</span>
              </div>
            </div>
            <details class="mcp-details">
              <summary>Raw Tool Call</summary>
              <div class="mcp-result-content">${toolCall.originalText || `mcpExecuteTool("${toolCall.server}", "${toolCall.tool}", ${JSON.stringify(toolCall.parameters || {})})`}</div>
            </details>
            <details class="mcp-details" ${this.formatToolResult(data).length <= 200 ? 'open' : ''}>
              <summary>Result (${this.formatToolResult(data).length} characters)</summary>
              <div class="mcp-result-content">${this.formatToolResult(data)}</div>
            </details>
          `
        },
        error: {
          background: 'linear-gradient(180deg, #dc3545 0%, #c82333 100%)',
          color: 'white',
          animation: 'mcpShake 0.6s ease-in-out',
          content: `
            <div class="mcp-widget-header">
              <div class="mcp-widget-title">
                <span class="mcp-widget-icon">❌</span>
                <span>MCP Tool Error: ${toolCall.server}/${toolCall.tool}</span>
              </div>
              <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
              <div class="mcp-status-row">
                <strong>Status:</strong>
                <span>❌ Failed</span>
              </div>
              <div class="mcp-status-row">
                <strong>Time:</strong>
                <span>${currentTime.toLocaleTimeString()}</span>
              </div>
              <div class="mcp-status-row">
                <strong>Duration:</strong>
                <span>${duration}s</span>
              </div>
            </div>
            <details class="mcp-details">
              <summary>Raw Tool Call</summary>
              <div class="mcp-result-content">${toolCall.originalText || `mcpExecuteTool("${toolCall.server}", "${toolCall.tool}", ${JSON.stringify(toolCall.parameters || {})})`}</div>
            </details>
            <details class="mcp-details">
              <summary>Error Details</summary>
              <div class="mcp-result-content">${data || 'Unknown error'}</div>
            </details>
          `
        }
      };

      const stateConfig = states[state];
      widget.style.background = stateConfig.background;
      widget.style.color = stateConfig.color;
      widget.style.animation = stateConfig.animation;
      widget.innerHTML = stateConfig.content;
    }

    startStopwatch(widget, startTime) {
      const stopwatchElement = widget.querySelector('.mcp-stopwatch');
      if (!stopwatchElement) return;

      const updateStopwatch = () => {
        const now = new Date();
        const elapsed = (now - startTime) / 1000;
        stopwatchElement.textContent = `${elapsed.toFixed(1)}s`;

        // Add subtle pulse effect for the stopwatch
        stopwatchElement.style.color = elapsed % 2 < 1 ? '#ffffff' : '#e0e0e0';
      };

      // Update immediately
      updateStopwatch();

      // Store interval ID on the widget for cleanup
      const intervalId = setInterval(updateStopwatch, 100);
      widget.stopwatchInterval = intervalId;

      // Auto-cleanup after 30 seconds to prevent memory leaks
      setTimeout(() => {
        if (widget.stopwatchInterval === intervalId) {
          clearInterval(intervalId);
          widget.stopwatchInterval = null;
        }
      }, 30000);
    }

    stopStopwatch(widget) {
      if (widget.stopwatchInterval) {
        clearInterval(widget.stopwatchInterval);
        widget.stopwatchInterval = null;
      }
    }

    // Seamless Mode Implementation
    initializeSeamlessMode() {
      console.log('[Perplexity MCP] Initializing seamless mode...');

      // Load thread state
      this.loadThreadState();

      // Set up dual textarea system
      this.setupDualTextareaSystem();

      // Monitor for response element count changes
      this.startSeamlessResponseMonitoring();

      console.log('[Perplexity MCP] Seamless mode initialized');
    }

    loadThreadState() {
      // Use the enhanced state loading function
      this.loadEnhancedThreadState();
    }

    saveThreadState() {
      // Use the enhanced state saving function
      this.saveEnhancedThreadState();
    }

    extractThreadId(url) {
      // Extract thread ID from Perplexity URL pattern
      const match = url.match(/\/search\/([a-f0-9-]+)/);
      return match ? match[1] : 'default';
    }

    // Enhanced state persistence system methods
    shouldEnableStatePersistence() {
      // Only enable state persistence for Perplexity search URLs
      const currentUrl = window.location.href;
      return currentUrl.startsWith('https://www.perplexity.ai/search/');
    }

    generateElementId(element) {
      // Generate a unique ID for an element based on its characteristics
      if (!element) return null;

      const tagName = element.tagName.toLowerCase();
      const className = element.className || '';
      const textContent = (element.textContent || '').substring(0, 50);
      const position = Array.from(element.parentNode?.children || []).indexOf(element);

      return `${tagName}_${className.replace(/\s+/g, '_')}_${position}_${btoa(textContent).substring(0, 10)}`;
    }

    captureElementState(element, stateType, additionalData = {}) {
      if (!this.shouldEnableStatePersistence() || !element) return;

      const elementId = this.generateElementId(element);
      if (!elementId) return;

      const state = {
        elementId,
        stateType,
        tagName: element.tagName,
        className: element.className,
        innerHTML: element.innerHTML,
        textContent: element.textContent,
        style: element.style.cssText,
        attributes: {},
        position: {
          parent: this.generateElementId(element.parentNode),
          index: Array.from(element.parentNode?.children || []).indexOf(element)
        },
        timestamp: Date.now(),
        ...additionalData
      };

      // Capture important attributes
      for (const attr of element.attributes || []) {
        state.attributes[attr.name] = attr.value;
      }

      this.stateManager.elementStates.set(elementId, state);
      this.stateManager.modifiedElements.set(elementId, element);

      // Throttled save
      this.throttledSaveState();

      console.log(`[Perplexity MCP] Captured ${stateType} state for element:`, elementId);
    }

    throttledSaveState() {
      const now = Date.now();
      if (now - this.stateManager.lastSaveTime > this.stateManager.saveThrottleMs) {
        this.stateManager.lastSaveTime = now;
        this.saveEnhancedThreadState();
      }
    }

    saveEnhancedThreadState() {
      if (!this.shouldEnableStatePersistence()) return;

      const currentUrl = window.location.href;
      const threadId = this.extractThreadId(currentUrl);

      if (threadId) {
        const enhancedState = {
          // Original state data
          activeToolCalls: Array.from(this.seamlessMode.activeToolCalls.entries()),
          lastUpdated: Date.now(),

          // Enhanced state data
          elementStates: Array.from(this.stateManager.elementStates.entries()),
          widgetStates: Array.from(this.stateManager.widgetStates.entries()),
          queryCleanupStates: Array.from(this.stateManager.queryCleanupStates.entries()),
          statusIndicatorStates: Array.from(this.stateManager.statusIndicatorStates.entries()),

          // Page metadata
          pageUrl: currentUrl,
          threadId: threadId,
          saveVersion: '2.0' // Version for compatibility
        };

        try {
          localStorage.setItem(`mcp_thread_${threadId}`, JSON.stringify(enhancedState));
          this.seamlessMode.threadState.set(threadId, enhancedState);
          console.log(`[Perplexity MCP] Enhanced state saved for thread: ${threadId}`);
        } catch (error) {
          console.warn('[Perplexity MCP] Failed to save enhanced state:', error);
        }
      }
    }

    loadEnhancedThreadState() {
      if (!this.shouldEnableStatePersistence()) return;

      const currentUrl = window.location.href;
      const threadId = this.extractThreadId(currentUrl);

      if (threadId) {
        const savedState = localStorage.getItem(`mcp_thread_${threadId}`);
        if (savedState) {
          try {
            const state = JSON.parse(savedState);

            // Check if this is an enhanced state (version 2.0+)
            if (state.saveVersion && state.elementStates) {
              console.log(`[Perplexity MCP] Loading enhanced state for thread: ${threadId}`);

              // Restore element states
              if (state.elementStates) {
                this.stateManager.elementStates = new Map(state.elementStates);
              }
              if (state.widgetStates) {
                this.stateManager.widgetStates = new Map(state.widgetStates);
              }
              if (state.queryCleanupStates) {
                this.stateManager.queryCleanupStates = new Map(state.queryCleanupStates);
              }
              if (state.statusIndicatorStates) {
                this.stateManager.statusIndicatorStates = new Map(state.statusIndicatorStates);
              }

              // Restore original seamless mode state
              if (state.activeToolCalls) {
                this.seamlessMode.activeToolCalls = new Map(state.activeToolCalls);
              }

              this.seamlessMode.threadState.set(threadId, state);

              // Schedule state restoration after page is ready
              setTimeout(() => {
                this.restorePageState();
              }, 2000);

            } else {
              // Legacy state format - just load basic data
              console.log(`[Perplexity MCP] Loading legacy state for thread: ${threadId}`);
              if (state.activeToolCalls) {
                this.seamlessMode.activeToolCalls = new Map(state.activeToolCalls);
              }
              this.seamlessMode.threadState.set(threadId, state);
            }

          } catch (e) {
            console.warn('[Perplexity MCP] Failed to parse enhanced thread state:', e);
          }
        }
      }
    }

    restorePageState() {
      if (!this.shouldEnableStatePersistence() || this.stateManager.restorationComplete) {
        return;
      }

      console.log('[Perplexity MCP] Starting page state restoration...');
      this.stateManager.isRestoring = true;

      try {
        // Restore widgets first
        this.restoreWidgets();

        // Restore modified elements
        this.restoreModifiedElements();

        // Restore query cleanup states
        this.restoreQueryCleanupStates();

        // Restore status indicators
        this.restoreStatusIndicators();

        this.stateManager.restorationComplete = true;
        console.log('[Perplexity MCP] Page state restoration completed');

      } catch (error) {
        console.error('[Perplexity MCP] Error during state restoration:', error);
      } finally {
        // Always reset the restoring flag after a delay
        setTimeout(() => {
          this.stateManager.isRestoring = false;
        }, 5000);
      }
    }

    restoreWidgets() {
      console.log('[Perplexity MCP] Restoring widgets...');

      for (const [widgetId, widgetState] of this.stateManager.widgetStates) {
        try {
          this.createRestoredWidget(widgetState);
        } catch (error) {
          console.warn(`[Perplexity MCP] Failed to restore widget ${widgetId}:`, error);
        }
      }
    }

    createRestoredWidget(widgetState) {
      // Create a visual-only widget that shows the previous state without executing tools
      const widget = document.createElement('div');
      widget.className = 'mcp-tool-widget restored';
      widget.dataset.mcpRestored = 'true';

      // Set the widget content based on saved state
      widget.innerHTML = `
        <div class="mcp-widget-header">
          <span class="mcp-widget-icon">🔧</span>
          <span class="mcp-widget-title">${widgetState.toolName || 'MCP Tool'}</span>
          <span class="mcp-widget-status ${widgetState.status || 'completed'}">${widgetState.statusText || 'Restored'}</span>
        </div>
        <div class="mcp-widget-content">
          ${widgetState.content || 'Tool execution result restored from previous session'}
        </div>
        <div class="mcp-widget-footer">
          <span class="mcp-widget-restored-notice">Restored from previous session</span>
        </div>
      `;

      // Try to find the target location for the widget
      if (widgetState.targetSelector) {
        const targetElement = document.querySelector(widgetState.targetSelector);
        if (targetElement) {
          targetElement.appendChild(widget);
          console.log('[Perplexity MCP] Restored widget to target location');
          return;
        }
      }

      // Fallback: append to a suitable container
      const container = document.querySelector('.pb-lg:last-child') || document.body;
      container.appendChild(widget);
      console.log('[Perplexity MCP] Restored widget to fallback location');
    }

    restoreModifiedElements() {
      console.log('[Perplexity MCP] Restoring modified elements...');

      for (const [elementId, elementState] of this.stateManager.elementStates) {
        try {
          this.restoreElementState(elementState);
        } catch (error) {
          console.warn(`[Perplexity MCP] Failed to restore element ${elementId}:`, error);
        }
      }
    }

    restoreElementState(elementState) {
      // Try to find the element by various methods
      let targetElement = null;

      // Method 1: Try to find by class and position
      if (elementState.className && elementState.position) {
        const candidates = document.querySelectorAll(`.${elementState.className.split(' ').join('.')}`);
        if (candidates[elementState.position.index]) {
          targetElement = candidates[elementState.position.index];
        }
      }

      // Method 2: Try to find by tag name and content
      if (!targetElement && elementState.textContent) {
        const candidates = document.querySelectorAll(elementState.tagName);
        for (const candidate of candidates) {
          if (candidate.textContent && candidate.textContent.includes(elementState.textContent.substring(0, 20))) {
            targetElement = candidate;
            break;
          }
        }
      }

      if (targetElement) {
        // Apply the saved state
        if (elementState.style) {
          targetElement.style.cssText = elementState.style;
        }

        // Apply saved attributes
        for (const [attrName, attrValue] of Object.entries(elementState.attributes || {})) {
          if (attrName !== 'style') {
            targetElement.setAttribute(attrName, attrValue);
          }
        }

        console.log(`[Perplexity MCP] Restored element state: ${elementState.stateType}`);
      }
    }

    restoreQueryCleanupStates() {
      console.log('[Perplexity MCP] Restoring query cleanup states...');

      for (const [queryId, cleanupState] of this.stateManager.queryCleanupStates) {
        try {
          this.applyQueryCleanup(cleanupState);
        } catch (error) {
          console.warn(`[Perplexity MCP] Failed to restore query cleanup ${queryId}:`, error);
        }
      }
    }

    applyQueryCleanup(cleanupState) {
      // Apply query cleanup based on saved state
      if (cleanupState.originalPrompt && cleanupState.selector) {
        const elements = document.querySelectorAll(cleanupState.selector);
        for (const element of elements) {
          if (element.textContent && element.textContent.includes('Available MCP Tools')) {
            element.textContent = cleanupState.originalPrompt;
            console.log('[Perplexity MCP] Applied query cleanup restoration');
          }
        }
      }
    }

    restoreStatusIndicators() {
      console.log('[Perplexity MCP] Restoring status indicators...');

      for (const [indicatorId, indicatorState] of this.stateManager.statusIndicatorStates) {
        try {
          this.restoreStatusIndicator(indicatorState);
        } catch (error) {
          console.warn(`[Perplexity MCP] Failed to restore status indicator ${indicatorId}:`, error);
        }
      }
    }

    restoreStatusIndicator(indicatorState) {
      const indicator = document.getElementById(indicatorState.elementId);
      if (indicator) {
        indicator.className = indicatorState.className;
        indicator.textContent = indicatorState.textContent;
        console.log(`[Perplexity MCP] Restored status indicator: ${indicatorState.elementId}`);
      }
    }

    captureWidgetState(widget, toolCall, responseElement) {
      if (!this.shouldEnableStatePersistence()) return;

      const widgetId = this.generateElementId(widget);
      if (!widgetId) return;

      const widgetState = {
        widgetId,
        toolName: toolCall.tool,
        serverId: toolCall.server,
        parameters: toolCall.parameters,
        status: 'pending',
        statusText: 'Executing...',
        content: '',
        targetSelector: this.generateSelectorForElement(responseElement),
        toolCall: {
          id: toolCall.id,
          tool: toolCall.tool,
          server: toolCall.server,
          parameters: toolCall.parameters,
          originalText: toolCall.originalText
        },
        timestamp: Date.now()
      };

      this.stateManager.widgetStates.set(widgetId, widgetState);

      // Also capture the response element state
      this.captureElementState(responseElement, 'widget-container', {
        widgetId: widgetId,
        toolCall: toolCall
      });

      console.log(`[Perplexity MCP] Captured widget state: ${widgetId}`);
    }

    generateSelectorForElement(element) {
      if (!element) return null;

      // Try to generate a reliable selector
      if (element.id) {
        return `#${element.id}`;
      }

      if (element.className) {
        const classes = element.className.split(' ').filter(c => c.trim()).slice(0, 3);
        if (classes.length > 0) {
          return `.${classes.join('.')}`;
        }
      }

      // Fallback to tag name with position
      const siblings = Array.from(element.parentNode?.children || []);
      const index = siblings.indexOf(element);
      return `${element.tagName.toLowerCase()}:nth-child(${index + 1})`;
    }

    captureQueryCleanupState(element, originalPrompt, selector) {
      if (!this.shouldEnableStatePersistence()) return;

      const cleanupId = this.generateElementId(element);
      if (!cleanupId) return;

      const cleanupState = {
        cleanupId,
        originalPrompt,
        selector,
        elementId: element.id,
        className: element.className,
        tagName: element.tagName,
        timestamp: Date.now()
      };

      this.stateManager.queryCleanupStates.set(cleanupId, cleanupState);
      console.log(`[Perplexity MCP] Captured query cleanup state: ${cleanupId}`);
    }

    updateWidgetState(widget, toolCall, status, result) {
      if (!this.shouldEnableStatePersistence() || !widget) return;

      const widgetId = this.generateElementId(widget);
      if (!widgetId) return;

      const existingState = this.stateManager.widgetStates.get(widgetId);
      if (existingState) {
        existingState.status = status;
        existingState.statusText = status === 'success' ? 'Completed' : 'Error';
        existingState.content = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
        existingState.lastUpdated = Date.now();

        this.stateManager.widgetStates.set(widgetId, existingState);
        this.throttledSaveState();

        console.log(`[Perplexity MCP] Updated widget state: ${widgetId} - ${status}`);
      }
    }

    setupDualTextareaSystem() {
      const originalTextarea = document.querySelector('textarea#ask-input');
      if (!originalTextarea) {
        console.log('[Perplexity MCP] Original textarea not found, retrying...');
        setTimeout(() => this.setupDualTextareaSystem(), 1000);
        return;
      }

      // Remove existing overlay if it exists
      const existingOverlay = document.getElementById('ask-input-mcp-overlay');
      if (existingOverlay) {
        existingOverlay.remove();
      }

      // Hide the original textarea (but keep it functional for React)
      originalTextarea.style.opacity = '0';
      originalTextarea.style.pointerEvents = 'none';

      // Store reference to original (this will handle the enhanced prompts)
      this.seamlessMode.hiddenTextarea = originalTextarea;

      // Create overlay textarea for user interaction
      this.seamlessMode.userTextarea = originalTextarea.cloneNode(true);
      this.seamlessMode.userTextarea.id = 'ask-input-mcp-overlay';
      this.seamlessMode.userTextarea.value = ''; // Start clean

      // Position overlay exactly over the original
      const rect = originalTextarea.getBoundingClientRect();
      this.seamlessMode.userTextarea.style.cssText = `
        position: absolute !important;
        top: ${originalTextarea.offsetTop}px !important;
        left: ${originalTextarea.offsetLeft}px !important;
        width: ${originalTextarea.offsetWidth}px !important;
        height: ${originalTextarea.offsetHeight}px !important;
        z-index: 10 !important;
        background: ${getComputedStyle(originalTextarea).background} !important;
        border: ${getComputedStyle(originalTextarea).border} !important;
        border-radius: ${getComputedStyle(originalTextarea).borderRadius} !important;
        font-family: ${getComputedStyle(originalTextarea).fontFamily} !important;
        font-size: ${getComputedStyle(originalTextarea).fontSize} !important;
        padding: ${getComputedStyle(originalTextarea).padding} !important;
        margin: 0 !important;
        resize: ${getComputedStyle(originalTextarea).resize} !important;
        outline: none !important;
        box-sizing: border-box !important;
      `;

      // Copy important attributes
      this.seamlessMode.userTextarea.placeholder = originalTextarea.placeholder;
      this.seamlessMode.userTextarea.className = originalTextarea.className;

      // Insert overlay into the same parent
      originalTextarea.parentNode.insertBefore(this.seamlessMode.userTextarea, originalTextarea);

      // Sync overlay changes back to original for React consistency
      this.setupTextareaSyncing();

      console.log('[Perplexity MCP] Overlay textarea system setup complete');
    }

    setupTextareaSyncing() {
      const overlay = this.seamlessMode.userTextarea;
      const original = this.seamlessMode.hiddenTextarea;

      // Sync user input from overlay to original (without enhancement)
      overlay.addEventListener('input', (e) => {
        // Keep original in sync with user input (for React state)
        this.sendTextInBackground(original, overlay.value);
      });

      // Set up event handlers on overlay textarea
      this.setupOverlayEventHandlers(overlay);

      // Handle resize
      const resizeObserver = new ResizeObserver(() => {
        if (original && overlay) {
          overlay.style.width = original.offsetWidth + 'px';
          overlay.style.height = original.offsetHeight + 'px';
        }
      });
      resizeObserver.observe(original);

      // Store resize observer for cleanup
      this.seamlessMode.resizeObserver = resizeObserver;
    }

    setupOverlayEventHandlers(overlay) {
      // Handle Enter key on overlay
      const handleOverlayEnter = (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
          if (e.mcpProcessed) return;

          console.log('[Perplexity MCP] 🚀 Enter key on overlay textarea');
          e.preventDefault();
          e.stopPropagation();
          this.handleSeamlessSubmission(overlay.value);
        }
      };

      overlay.addEventListener('keydown', handleOverlayEnter, { capture: true });

      // Also handle other keyboard shortcuts that might trigger submission
      overlay.addEventListener('keydown', (e) => {
        // Cmd/Ctrl + Enter for submission
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
          if (e.mcpProcessed) return;

          console.log('[Perplexity MCP] 🚀 Ctrl/Cmd+Enter on overlay textarea');
          e.preventDefault();
          e.stopPropagation();
          this.handleSeamlessSubmission(overlay.value);
        }
      }, { capture: true });

      console.log('[Perplexity MCP] Overlay event handlers setup complete');
    }

    startSeamlessResponseMonitoring() {
      // Count initial response elements
      this.seamlessMode.responseElementCount = document.querySelectorAll('div.-inset-md.absolute').length;

      const observer = new MutationObserver(() => {
        this.checkForResponseChanges();
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      this.seamlessMode.responseObserver = observer;
    }

    checkForResponseChanges() {
      const currentCount = document.querySelectorAll('div.-inset-md.absolute').length;

      if (currentCount > this.seamlessMode.responseElementCount) {
        // New response element detected
        this.handleNewResponse();
        this.seamlessMode.responseElementCount = currentCount;
      }
    }

    handleNewResponse() {
      console.log('[Perplexity MCP] New response detected. Pending deletions:', this.seamlessMode.pendingDeletions.length);

      // Check if we have pending deletions to process (only for tool call responses)
      if (this.seamlessMode.pendingDeletions.length > 0) {
        const deletion = this.seamlessMode.pendingDeletions.shift();
        console.log('[Perplexity MCP] Processing pending deletion for tool call:', deletion.toolCall?.tool);
        this.processResponseDeletion(deletion);
      } else {
        console.log('[Perplexity MCP] No pending deletions - this is a normal AI response');
      }
    }

    processResponseDeletion(deletion) {
      // Only process deletions for responses that actually contained tool calls
      if (!deletion.toolCall || !deletion.elementToDelete) {
        console.log('[Perplexity MCP] Skipping deletion - no tool call or element reference');
        return;
      }

      console.log('[Perplexity MCP] Processing deletion for tool call response:', deletion.toolCall.tool);

      // Find the last response element
      const responseElements = document.querySelectorAll('div.-inset-md.absolute');
      const lastElement = responseElements[responseElements.length - 1];

      if (lastElement) {
        // Verify this is actually a tool call response by checking if it contains the tool call element
        const toolCallElement = deletion.elementToDelete;
        if (!document.body.contains(toolCallElement)) {
          console.log('[Perplexity MCP] Tool call element no longer in DOM, proceeding with deletion');
        }

        // Go up 4 levels to find the container to delete
        let targetElement = lastElement;
        for (let i = 0; i < 4; i++) {
          if (targetElement.parentElement) {
            targetElement = targetElement.parentElement;
          }
        }

        // Go up one more level to find the parent with .md\:sticky element
        const parentElement = targetElement.parentElement;
        if (parentElement) {
          // Find and delete .md\:sticky element
          const stickyElement = parentElement.querySelector('.md\\:sticky');
          if (stickyElement) {
            stickyElement.remove();
            console.log('[Perplexity MCP] Removed sticky element for tool call response');
          }

          // Delete the main response element
          targetElement.remove();

          console.log('[Perplexity MCP] Processed response deletion for tool call in seamless mode');
        }
      } else {
        console.log('[Perplexity MCP] No response elements found for deletion');
      }
    }

    async executeInlineToolCall(toolCall, widget) {
      // Prevent execution during restoration
      if (this.stateManager.isRestoring) {
        console.log('[Perplexity MCP] Skipping tool execution during restoration');
        return null;
      }

      // Add start time for duration tracking
      toolCall.startTime = new Date();

      // Start the stopwatch
      this.startStopwatch(widget, toolCall.startTime);

      try {
        // Check if MCP bridge is enabled
        if (!this.settings.bridgeEnabled) {
          this.setWidgetState(widget, 'error', toolCall, 'MCP bridge disabled');
          throw new Error('MCP bridge disabled');
        }

        // Check if autoExecute is enabled
        if (!this.settings.autoExecute) {
          this.setWidgetState(widget, 'error', toolCall, 'Auto-execution disabled');
          throw new Error('Auto-execution disabled');
        }

        // Check if server is enabled
        const serverId = toolCall.server;
        const serverSetting = this.settings.serverSettings ? this.settings.serverSettings[serverId] : undefined;
        if (serverSetting && serverSetting.enabled === false) {
          this.setWidgetState(widget, 'error', toolCall, `Server ${serverId} disabled`);
          throw new Error(`Server ${serverId} disabled`);
        }

        // Check if connected
        if (!this.isConnected) {
          this.setWidgetState(widget, 'error', toolCall, 'Not connected to MCP bridge');
          throw new Error('Not connected to MCP bridge');
        }

        // Execute the tool
        const result = await this.executeToolInContext(
          serverId,
          toolCall.tool,
          toolCall.parameters || {}
        );

        // Stop the stopwatch and show success state
        this.stopStopwatch(widget);
        this.setWidgetState(widget, 'success', toolCall, result);

        // Update widget state with result
        this.updateWidgetState(widget, toolCall, 'success', result);

        // Inject follow-up prompt for continued conversation (only in legacy mode)
        if (this.settings.legacyMode) {
          setTimeout(() => {
            this.injectFollowUpPrompt(result, toolCall);
          }, 500);
        }

        // Return the result for seamless mode processing
        return result;

      } catch (error) {
        console.error('[Perplexity MCP] Inline tool execution failed:', error);
        this.stopStopwatch(widget);
        this.setWidgetState(widget, 'error', toolCall, error.message);

        // Update widget state with error
        this.updateWidgetState(widget, toolCall, 'error', error.message);

        // Re-throw the error so it can be caught by the caller
        throw error;
      }
    }


    formatToolResult(result) {
      if (typeof result === 'string') return result;
      if (result && result.content) {
        if (Array.isArray(result.content)) {
          return result.content.map(item =>
            typeof item === 'string' ? item : JSON.stringify(item, null, 2)
          ).join('\n');
        }
        return result.content;
      }
      return JSON.stringify(result, null, 2);
    }

    injectFollowUpPrompt(toolResult, toolCall) {
      // In seamless mode, tool results are handled automatically
      if (!this.settings.legacyMode) {
        console.log('[Perplexity MCP] Seamless mode: follow-up handled automatically');
        return;
      }

      // Legacy mode: inject follow-up prompt
      const input = this.findActiveInput();
      if (input) {
        const toolInfo = toolCall ? `${toolCall.server}/${toolCall.tool}` : 'MCP tool';
        const fullResult = this.formatToolResult(toolResult);
        const contextPrompt = `[Previous ${toolInfo} result: ${fullResult}]`;

        setTimeout(() => {
          const currentValue = input.value || input.textContent || '';
          if (!currentValue.includes('[Previous ') && !currentValue.includes('MCP tool result:')) {
            if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
              this.sendTextInBackground(input, contextPrompt);
            } else {
              input.textContent = contextPrompt;
              input.dispatchEvent(new Event('input', { bubbles: true }));
            }
            console.log('[Perplexity MCP] Injected follow-up context for next query (legacy mode)');

            // Auto-submit the follow-up prompt after a short delay
            setTimeout(() => {
              console.log('[Perplexity MCP] Auto-submitting follow-up prompt with tool results');

              // Use background submission method
              this.submitTextInBackground(input);
              console.log('[Perplexity MCP] Legacy mode: Submitted follow-up prompt using background method.');
            }, 200); // Give time for React to process the input change
          }
        }, 1000);
      }
    }


    findActiveInput() {
      // Only use the specific textarea as requested
      return this.promptInput || document.querySelector('textarea#ask-input');
    }

    // Open server details in settings page
    openServerDetails(serverId = null, toolId = null) {
      // Construct the settings URL with hash-based routing for Chrome extension compatibility
      const baseUrl = chrome.runtime.getURL('settings.html');
      let settingsUrl;

      if (serverId) {
        const encodedServerId = encodeURIComponent(serverId);
        if (toolId) {
          const encodedToolId = encodeURIComponent(toolId);
          settingsUrl = `${baseUrl}#/servers/${encodedServerId}/${encodedToolId}`;
        } else {
          settingsUrl = `${baseUrl}#/servers/${encodedServerId}`;
        }
      } else {
        // Just open servers section
        settingsUrl = `${baseUrl}#/servers`;
      }

      // Send message to background script to open the tab (chrome.tabs not available in content scripts)
      chrome.runtime.sendMessage({
        type: 'open_tab',
        url: settingsUrl
      });
    }

    // ...existing code...
  }

  // Initialize MCP client only if not already present
  if (!window.mcpClient) {
    const mcpClient = new PerplexityMcpClient();
    // Make it globally available
    window.mcpClient = mcpClient;
    console.log('[Perplexity MCP] Content script loaded and initialized');
  } else {
    console.log('[Perplexity MCP] Client already exists, skipping initialization');
  }

})(); // End IIFE